<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hvisions</groupId>
    <artifactId>file-management</artifactId>
    <version>1.9.4</version>
    <modules>
        <module>file-management-server</module>
        <module>file-management-client</module>
        <module>file-management-common</module>
    </modules>
    <packaging>pom</packaging>

    <name>file-management</name>
    <description>HVisions-micro-service-file-management</description>
    <repositories>
        <repository>
            <id>nexus</id>
            <name>Nexus Repository</name>
            <url>http://**************:8022/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <!--snapshots默认是关闭的,需要开启  -->
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>

    </repositories>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.5.RELEASE</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>
    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://**************:8022/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshot</id>
            <url>http://**************:8022/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <docker.image.prefix>hvisions</docker.image.prefix>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-cloud.version>Finchley.SR1</spring-cloud.version>
        <!--跳过测试-->
        <maven.test.skip>true</maven.test.skip>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.hvisions</groupId>
                <artifactId>file-management-common</artifactId>
                <version>1.9.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <scm>
        <developerConnection>scm:git:http://************:81/leiming/hvisions-file-management.git</developerConnection>
        <tag>file-management-1.9.4</tag>
    </scm>
    <build>
        <plugins>
            <!-- 发布插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <arguments>-DskipTests</arguments>
                </configuration>
            </plugin>
            <!--屏蔽源代码上传-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <skipSource>true</skipSource>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

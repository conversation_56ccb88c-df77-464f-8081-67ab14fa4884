package com.hvisions.bpm.framework.dict.config;

import com.hvisions.bpm.framework.dict.core.DictFrameworkUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
public class YudaoDictAutoConfiguration {

//    @Bean
//    @SuppressWarnings("InstantiationOfUtilityClass")
//    public DictFrameworkUtils dictUtils(DictDataApi dictDataApi) {
//        DictFrameworkUtils.init(dictDataApi);
//        return new DictFrameworkUtils();
//    }

}

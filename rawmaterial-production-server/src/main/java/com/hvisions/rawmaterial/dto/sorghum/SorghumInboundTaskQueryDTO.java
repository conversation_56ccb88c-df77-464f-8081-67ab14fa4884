package com.hvisions.rawmaterial.dto.sorghum;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 高粱入仓任务查询DTO
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Data
@ApiModel(description = "高粱入仓任务查询DTO")
public class SorghumInboundTaskQueryDTO extends PageInfo {

    @ApiModelProperty(value = "入仓编号")
    private String siloCode;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "状态（0：未完成，1：已完成）")
    private Integer status;

    @ApiModelProperty(value = "工单日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDateStart;

    @ApiModelProperty(value = "工单日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDateEnd;

    @ApiModelProperty(value = "工单号")
    private String orderNo;
}
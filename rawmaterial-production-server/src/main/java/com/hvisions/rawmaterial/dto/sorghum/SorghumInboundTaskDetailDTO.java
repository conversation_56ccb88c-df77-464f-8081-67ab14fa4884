package com.hvisions.rawmaterial.dto.sorghum;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 高粱入仓任务明细DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "高粱入仓任务明细DTO")
public class SorghumInboundTaskDetailDTO{

    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private Integer taskId;

    /**
     * 入仓名称
     */
    @ApiModelProperty(value = "入仓名称")
    private String siloName;

    /**
     * 入仓编码
     */
    @ApiModelProperty(value = "入仓编码")
    private String siloCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 计量 单位
     */
    @ApiModelProperty(value = "计量 单位")
    private String unit;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualStartTime;

    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    private Date actualCompletionTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorName;

    /**
     * 唯一标识
     */
    @ApiModelProperty(value = "唯一标识")
    private String uniqueId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
package com.hvisions.rawmaterial.dao.ricehusk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskQueryDTO;
import com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 稻壳入仓任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface RiceHuskInboundTaskMapper extends BaseMapper<TMpdRiceHuskInboundTask> {

    /**
     * 根据条件查询稻壳入仓任务列表
     * 
     * @param query 查询条件
     * @return 稻壳入仓任务列表
     */
    List<TMpdRiceHuskInboundTask> selectByCondition(RiceHuskInboundTaskQueryDTO query);

    /**
     * 根据工单日期查询当天最大流水号
     *
     * @param orderDate 工单日期
     * @return 最大流水号
     */
    String selectMaxSerialNoByDate(@Param("orderDate") Date orderDate);

    /**
     * 根据工单日期查询当天的任务
     *
     * @param orderDate 工单日期
     * @return 当天的任务
     */
    TMpdRiceHuskInboundTask selectByOrderDate(@Param("orderDate") Date orderDate);

    /**
     * 根据日期和物料编码查询任务
     *
     * @param dateStr 日期字符串（YYYYMMDD格式）
     * @param materialCode 物料编码
     * @return 任务记录
     */
    TMpdRiceHuskInboundTask selectByDateAndMaterialCode(@Param("dateStr") String dateStr, @Param("materialCode") String materialCode);

    /**
     * 分页查询稻壳入仓任务列表
     *
     * @param riceHuskInboundTaskQueryDTO 查询条件
     * @return 稻壳入仓任务列表
     */
    List<RiceHuskInboundTaskDTO> getRiceHuskInboundTaskPageList(RiceHuskInboundTaskQueryDTO riceHuskInboundTaskQueryDTO);
}

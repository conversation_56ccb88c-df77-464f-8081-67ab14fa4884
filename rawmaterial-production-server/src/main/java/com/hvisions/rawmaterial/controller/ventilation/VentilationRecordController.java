package com.hvisions.rawmaterial.controller.ventilation;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.rawmaterial.dto.VentilationRecordDTO;
import com.hvisions.rawmaterial.dto.VentilationRecordQueryDTO;
import com.hvisions.rawmaterial.dto.VentilationTemperatureMaintainDTO;
import com.hvisions.rawmaterial.service.VentilationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 通风记录控制器
 * @Date: 2024/07/14
 */
@Slf4j
@RestController
@RequestMapping("/ventilation-record")
@Api(tags = "通风记录管理")
public class VentilationRecordController {

    @Autowired
    private VentilationRecordService ventilationRecordService;

    /**
     * 创建通风记录（中控系统同步接口）
     */
    @PostMapping("/create")
    @ApiOperation("创建通风记录")
    public Integer createVentilationRecord(@Valid @RequestBody VentilationRecordDTO dto) {
        log.info("创建通风记录，筒仓：{}，物料：{}", dto.getSiloName(), dto.getMaterialName());
        return ventilationRecordService.createVentilationRecord(dto);
    }

    /**
     * 完成通风记录（中控系统同步接口）
     */
    @PostMapping("/complete")
    @ApiOperation("完成通风记录")
    public Boolean completeVentilationRecord(
            @ApiParam(value = "中控系统任务ID", required = true) @RequestParam String centralControlTaskId,
            @ApiParam(value = "结束时间", required = true) @RequestParam Date endTime) {
        log.info("完成通风记录，中控任务ID：{}", centralControlTaskId);
        return ventilationRecordService.completeVentilationRecord(centralControlTaskId, endTime);
    }

    /**
     * 维护温度信息
     */
    @PostMapping("/maintain-temperature")
    @ApiOperation("维护温度信息")
    public Boolean maintainTemperature(@Valid @RequestBody VentilationTemperatureMaintainDTO dto, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        log.info("维护温度信息，通风记录ID：{}", dto.getId());
        if (userInfo == null){
            throw new BaseKnownException("请重新登录");
        }
        return ventilationRecordService.maintainTemperature(dto, userInfo);
    }

    /**
     * 分页查询通风记录
     */
    @PostMapping("/query")
    @ApiOperation("分页查询通风记录")
    public Page<VentilationRecordDTO> queryVentilationRecords(@RequestBody VentilationRecordQueryDTO queryDTO) {
        return ventilationRecordService.queryVentilationRecords(queryDTO);
    }

    /**
     * 根据ID查询通风记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询通风记录详情")
    public VentilationRecordDTO getVentilationRecordById(@ApiParam(value = "通风记录ID", required = true) @PathVariable Integer id) {
        return ventilationRecordService.getVentilationRecordById(id);
    }

    /**
     * 根据工单号查询通风记录
     */
    @GetMapping("/work-order/{workOrderNumber}")
    @ApiOperation("根据工单号查询通风记录")
    public VentilationRecordDTO getVentilationRecordByWorkOrderNumber(
            @ApiParam(value = "工单号", required = true) @PathVariable String workOrderNumber) {
        return ventilationRecordService.getVentilationRecordByWorkOrderNumber(workOrderNumber);
    }

    /**
     * 查询执行中的通风记录
     */
    @GetMapping("/executing")
    @ApiOperation("查询执行中的通风记录")
    public List<VentilationRecordDTO> getExecutingRecords() {
        return ventilationRecordService.getExecutingRecords();
    }

    /**
     * 删除通风记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除通风记录")
    public Boolean deleteVentilationRecord(@ApiParam(value = "通风记录ID", required = true) @PathVariable Integer id) {
        log.info("删除通风记录，ID：{}", id);
        return ventilationRecordService.deleteVentilationRecord(id);
    }

    /**
     * 生成工单号
     */
    @GetMapping("/generate-work-order-number")
    @ApiOperation("生成工单号")
    public String generateWorkOrderNumber() {
        return ventilationRecordService.generateWorkOrderNumber();
    }
}

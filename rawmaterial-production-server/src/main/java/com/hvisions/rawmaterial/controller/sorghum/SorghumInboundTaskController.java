package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDTO;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.service.sorghum.SorghumInboundTaskService;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 高粱入仓任务Controller
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RestController
@RequestMapping("/sorghum/inbound-task")
@Api(tags = "高粱入仓任务管理")
public class SorghumInboundTaskController {

    @Autowired
    private SorghumInboundTaskService sorghumInboundTaskService;

    //@Autowired
    //private SorghumInboundTaskDetailService sorghumInboundTaskDetailService;

    @ApiOperation("分页查询高粱入仓任务列表")
    @PostMapping("/page")
    public Page<SorghumInboundTaskDTO> findByPage(@RequestBody SorghumInboundTaskQueryDTO queryDTO) {
        return sorghumInboundTaskService.findByPage(queryDTO);
    }


    /*@ApiOperation("查询高粱入仓任务列表-子列表")
    @GetMapping("/child-list")
    public List<SorghumInboundTaskDetailDTO> findChildList(@RequestParam("parentId") String parentId) {
        return sorghumInboundTaskDetailService.findByTaskId(parentId);
    }*/

    @ApiOperation("保存高粱入仓任务")
    @PostMapping
    public ResultVO<SorghumInboundTaskDTO> save(@RequestBody SorghumInboundTaskDTO dto) {
        try {
            SorghumInboundTaskDTO result = sorghumInboundTaskService.save(dto);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "保存高粱入仓任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新高粱入仓任务")
    @PutMapping
    public ResultVO<SorghumInboundTaskDTO> update(@RequestBody SorghumInboundTaskDTO dto) {
        try {
            SorghumInboundTaskDTO result = sorghumInboundTaskService.update(dto);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "更新高粱入仓任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除高粱入仓任务")
    @DeleteMapping("/{id}")
    public ResultVO<Void> delete(@PathVariable("id") String id) {
        try {
            sorghumInboundTaskService.delete(id);
            return ResultVO.success(null);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "删除高粱入仓任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("从中控同步入仓记录")
    @PostMapping("/sync")
    public ResultVO<String> syncFromCentralControl(@RequestBody UnifiedTaskDTO taskDTO) {
        try {
            String result = sorghumInboundTaskService.syncFromCentralControl(taskDTO);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "同步入仓记录失败：" + e.getMessage());
        }
    }
}
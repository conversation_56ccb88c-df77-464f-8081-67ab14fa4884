package com.hvisions.rawmaterial.consts;

/**
 * 筒仓倒仓任务常量类
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓倒仓任务相关常量定义
 * @Date: 2025-01-17
 */
public class SiloTransferConstants {

    // ==================== 状态常量 ====================
    
    /**
     * 状态：待执行
     */
    public static final Integer STATUS_PENDING = 1;
    
    /**
     * 状态：执行中
     */
    public static final Integer STATUS_IN_PROGRESS = 2;
    
    /**
     * 状态：已完成
     */
    public static final Integer STATUS_COMPLETED = 3;
    
    /**
     * 状态：失败
     */
    public static final Integer STATUS_FAILED = 4;
    
    /**
     * 状态：已取消
     */
    public static final Integer STATUS_CANCELLED = 5;

    // ==================== 状态描述常量 ====================
    
    /**
     * 状态描述：待执行
     */
    public static final String STATUS_PENDING_DESC = "待执行";
    
    /**
     * 状态描述：执行中
     */
    public static final String STATUS_IN_PROGRESS_DESC = "执行中";
    
    /**
     * 状态描述：已完成
     */
    public static final String STATUS_COMPLETED_DESC = "已完成";
    
    /**
     * 状态描述：失败
     */
    public static final String STATUS_FAILED_DESC = "失败";
    
    /**
     * 状态描述：已取消
     */
    public static final String STATUS_CANCELLED_DESC = "已取消";

    // ==================== 业务系统常量 ====================
    
    /**
     * 业务系统：中粮
     */
    public static final String BUSINESS_SYSTEM_ZHONGLIANG = "ZHONGLIANG";
    
    /**
     * 业务系统：布勒
     */
    public static final String BUSINESS_SYSTEM_BULER = "BULER";
    
    /**
     * 业务系统：捷赛
     */
    public static final String BUSINESS_SYSTEM_JIESAI = "JIESAI";

    // ==================== 物料类型常量 ====================
    
    /**
     * 物料类型：高粱
     */
    public static final String MATERIAL_TYPE_SORGHUM = "SORGHUM";
    
    /**
     * 物料类型：稻壳
     */
    public static final String MATERIAL_TYPE_RICE_HUSK = "RICE_HUSK";

    // ==================== 数据类型常量 ====================
    
    /**
     * 数据类型：任务
     */
    public static final String DATA_TYPE_TASK = "TASK";
    
    /**
     * 数据类型：实时数据
     */
    public static final String DATA_TYPE_REALTIME = "REALTIME";

    // ==================== 数据来源常量 ====================
    
    /**
     * 数据来源：中控系统
     */
    public static final String DATA_SOURCE_CENTRAL_CONTROL = "CENTRAL_CONTROL";
    
    /**
     * 数据来源：手工录入
     */
    public static final String DATA_SOURCE_MANUAL = "MANUAL";
    
    /**
     * 数据来源：接口同步
     */
    public static final String DATA_SOURCE_INTERFACE = "INTERFACE";

    // ==================== 进度常量 ====================
    
    /**
     * 进度：0%
     */
    public static final Integer PROGRESS_START = 0;
    
    /**
     * 进度：100%
     */
    public static final Integer PROGRESS_COMPLETE = 100;

    // ==================== 工具方法 ====================
    
    /**
     * 根据状态码获取状态描述
     * @param status 状态码
     * @return 状态描述
     */
    public static String getStatusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return STATUS_PENDING_DESC;
            case 2:
                return STATUS_IN_PROGRESS_DESC;
            case 3:
                return STATUS_COMPLETED_DESC;
            case 4:
                return STATUS_FAILED_DESC;
            case 5:
                return STATUS_CANCELLED_DESC;
            default:
                return "未知状态";
        }
    }
    
    /**
     * 检查业务系统是否有效
     * @param businessSystem 业务系统
     * @return 是否有效
     */
    public static boolean isValidBusinessSystem(String businessSystem) {
        return BUSINESS_SYSTEM_ZHONGLIANG.equals(businessSystem) ||
               BUSINESS_SYSTEM_BULER.equals(businessSystem) ||
               BUSINESS_SYSTEM_JIESAI.equals(businessSystem);
    }
    
    /**
     * 检查物料类型是否有效
     * @param materialType 物料类型
     * @return 是否有效
     */
    public static boolean isValidMaterialType(String materialType) {
        return MATERIAL_TYPE_SORGHUM.equals(materialType) ||
               MATERIAL_TYPE_RICE_HUSK.equals(materialType);
    }
    
    /**
     * 检查数据类型是否有效
     * @param dataType 数据类型
     * @return 是否有效
     */
    public static boolean isValidDataType(String dataType) {
        return DATA_TYPE_TASK.equals(dataType) ||
               DATA_TYPE_REALTIME.equals(dataType);
    }
    
    /**
     * 检查状态是否有效
     * @param status 状态
     * @return 是否有效
     */
    public static boolean isValidStatus(Integer status) {
        return status != null && (status >= STATUS_PENDING && status <= STATUS_CANCELLED);
    }
    
    /**
     * 检查状态是否为终态（已完成、失败、已取消）
     * @param status 状态
     * @return 是否为终态
     */
    public static boolean isFinalStatus(Integer status) {
        return status != null && (status == STATUS_COMPLETED || 
                                 status == STATUS_FAILED || 
                                 status == STATUS_CANCELLED);
    }
    
    /**
     * 检查状态是否可以取消
     * @param status 状态
     * @return 是否可以取消
     */
    public static boolean canCancel(Integer status) {
        return status != null && (status == STATUS_PENDING || status == STATUS_IN_PROGRESS);
    }
}

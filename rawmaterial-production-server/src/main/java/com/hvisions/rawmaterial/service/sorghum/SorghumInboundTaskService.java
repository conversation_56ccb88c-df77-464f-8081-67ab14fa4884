package com.hvisions.rawmaterial.service.sorghum;

import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDTO;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;

/**
 * 高粱入仓任务Service接口
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
public interface SorghumInboundTaskService {

    /**
     * 分页查询高粱入仓任务列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<SorghumInboundTaskDTO> findByPage(SorghumInboundTaskQueryDTO queryDTO);

    /**
     * 根据ID查询高粱入仓任务
     *
     * @param id 任务ID
     * @return 高粱入仓任务
     */
    SorghumInboundTaskDTO findById(String id);

    /**
     * 保存高粱入仓任务
     *
     * @param dto 高粱入仓任务DTO
     * @return 保存后的高粱入仓任务
     */
    SorghumInboundTaskDTO save(SorghumInboundTaskDTO dto);

    /**
     * 更新高粱入仓任务
     *
     * @param dto 高粱入仓任务DTO
     * @return 更新后的高粱入仓任务
     */
    SorghumInboundTaskDTO update(SorghumInboundTaskDTO dto);

    /**
     * 删除高粱入仓任务
     *
     * @param id 任务ID
     */
    void delete(String id);

    /**
     * 从中控同步入仓记录
     *
     * @param taskDTO 任务数据
     * @return 同步结果
     */
    String syncFromCentralControl(UnifiedTaskDTO taskDTO);
}
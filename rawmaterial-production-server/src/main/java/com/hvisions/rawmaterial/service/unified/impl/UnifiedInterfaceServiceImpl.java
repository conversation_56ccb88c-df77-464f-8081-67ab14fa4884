package com.hvisions.rawmaterial.service.unified.impl;

import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.enums.unified.BusinessSystemEnum;
import com.hvisions.rawmaterial.enums.unified.TaskTypeEnum;
import com.hvisions.rawmaterial.service.ricehusk.RiceHuskInboundTaskService;
import com.hvisions.rawmaterial.service.silo.SiloRealtimeStatusService;
import com.hvisions.rawmaterial.service.sorghum.SorghumInboundTaskService;
import com.hvisions.rawmaterial.service.unified.UnifiedInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 统一接口服务实现类
 * 根据TaskTypeEnum的不同类型实现相应的业务逻辑
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@Service
public class UnifiedInterfaceServiceImpl implements UnifiedInterfaceService {

    @Resource
    private SorghumInboundTaskService sorghumInboundTaskService;

    @Resource
    private RiceHuskInboundTaskService riceHuskInboundTaskService;

    @Autowired
    private SiloRealtimeStatusService siloRealtimeStatusService;


    // ==================== 任务相关方法实现 ====================

    /**
     * 创建任务
     * 根据任务类型枚举执行不同的业务逻辑
     *
     * @param taskDTO 任务DTO
     * @param request HTTP请求
     * @return 创建后的任务DTO
     */
    @Override
    public String createTask(UnifiedTaskDTO taskDTO, HttpServletRequest request) {
        log.info("开始创建任务：业务系统={}, 任务类型={}, 任务号={}",
                taskDTO.getBusinessSystem(), taskDTO.getTaskType(), taskDTO.getTaskNo());

        // 验证业务系统枚举
        BusinessSystemEnum businessSystemEnum = BusinessSystemEnum.getByCode(taskDTO.getBusinessSystem());
        if (businessSystemEnum == null) {
            throw new IllegalArgumentException("不支持的业务系统：" + taskDTO.getBusinessSystem());
        }

        // 获取任务类型枚举
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getByCode(taskDTO.getTaskType());
        if (taskTypeEnum == null) {
            throw new IllegalArgumentException("不支持的任务类型：" + taskDTO.getTaskType());
        }

        // 设置创建时间
        taskDTO.setCreateTime(new Date());

        // 根据任务类型执行相应的业务逻辑
        switch (taskTypeEnum) {
            case INBOUND://入仓==中控系统二期筒仓收货记录推送给MES
                return handleInbound(taskDTO);
            case SILO_REALTIME_STATUS://筒仓实时状态信息(含一期、3*17、1*7、225仓)
                return handleSiloRealtimeStatus(taskDTO);
            default:
                throw new UnsupportedOperationException("暂不支持的任务类型：" + taskTypeEnum.getDescription());
        }
    }

    // ==================== 高粱业务任务处理方法 ====================

    /**
     * 处理入仓任务
     */
    private String handleInbound(UnifiedTaskDTO taskDTO) {
        log.info("处理高粱二期筒仓收货接口（入仓）：任务号={}", taskDTO.getTaskNo());
        // 验证必要字段
        validateSorghumInboundFields(taskDTO);

        // 业务逻辑：记录收货信息
        log.info("收货仓号：{}, 收货重量：{}", taskDTO.getSiloNo(), taskDTO.getActualWeight());

        // 调用具体的业务服务
        // 1. 更新筒仓库存
        // 2. 记录入仓日志
        if (taskDTO.getMaterialType().equals("SORGHUM")){
            return  sorghumInboundTaskService.syncFromCentralControl(taskDTO);
        }else if (taskDTO.getMaterialType().equals("RICE_HUSK")){
            return  riceHuskInboundTaskService.syncFromCentralControl(taskDTO);
        }else{
            throw new UnsupportedOperationException("暂不支持的物料类型：" + taskDTO.getMaterialType());
        }
    }


    /**
     * 处理筒仓实时状态信息
     */
    private String handleSiloRealtimeStatus(UnifiedTaskDTO taskDTO) {
        log.info("处理高粱筒仓实时状态接口：任务号={}", taskDTO.getTaskNo());
        // 验证必要字段
        validateSiloRealtimeStatusFields(taskDTO);

        // 业务逻辑：更新筒仓库存
        siloRealtimeStatusService.syncRealtimeDataFromCentralControl(taskDTO);
        return "处理成功";
    }

    private void validateSiloRealtimeStatusFields(UnifiedTaskDTO taskDTO) {
        if (taskDTO.getSiloNo() == null) {
            throw new IllegalArgumentException("筒仓号不能为空");
        }

        /*if (taskDTO.getInventoryWeight() == null) {
            throw new IllegalArgumentException("库存重量不能为空");
        }*/
    }


    /**
     * 验证高粱入仓字段
     */
    private void validateSorghumInboundFields(UnifiedTaskDTO taskDTO) {
        if (taskDTO.getSiloNo() == null) {
            throw new IllegalArgumentException("收货仓号不能为空");
        }
        if (taskDTO.getActualWeight() == null) {
            throw new IllegalArgumentException("收货重量不能为空");
        }
    }

}

package com.hvisions.log.capture.client.fallback;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import org.springframework.stereotype.Component;

/**
 * <p>Title: LogCaptureFallBackFacotry</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class LogCaptureFallBackFacotry extends BaseFallbackFactory<LogCaptureClient> {
    /**
     * 获取Client对象
     *
     * @param vo 返回信息
     * @return Client对象
     */
    @Override
    public LogCaptureClient getFallBack(ResultVO vo) {
        return new LogCaptureClient() {
            @Override
            public ResultVO<Integer> logRecord(LogDto logDto) {
                return vo;
            }

            @Override
            public ResultVO<Integer> logRecordPiToMes(LogDto logDto) {
                return vo;
            }

            @Override
            public ResultVO<Integer> logRecordMesToPi(LogDto logDto) {
                return vo;
            }
        };
    }
}










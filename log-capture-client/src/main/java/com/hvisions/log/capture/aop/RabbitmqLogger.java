package com.hvisions.log.capture.aop;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.log.capture.common.interfaces.ILog;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

/**
 * <p>Title: RabbitmqLogger</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/6/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public class RabbitmqLogger implements ILog {
    private final RabbitTemplate rabbitTemplate;

    public RabbitmqLogger(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    /**
     * 发布外部接口
     *
     * @param logDto 日志信息
     * @return 日志记录id
     */
    @Override
    public ResultVO<Integer> logRecord(LogDto logDto) {
        rabbitTemplate.convertAndSend("h-visions.syslog", logDto.getControllerName() + "." + logDto.getMethodName(), logDto);
        return null;
    }

    @Override
    public ResultVO<Integer> logRecordPiToMes(LogDto logDto) {
        return null;
    }

    @Override
    public ResultVO<Integer> logRecordMesToPi(LogDto logDto) {
        return null;
    }
}










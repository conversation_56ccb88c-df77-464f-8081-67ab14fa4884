package com.hvisions.log.capture.client.fallback;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.DataAuditClient;
import com.hvisions.log.capture.common.dto.DataAuditDTO;
import org.springframework.stereotype.Component;

/**
 * <p>Title: LogCaptureFallBackFacotry</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class DataAuditFallBackFacotry extends BaseFallbackFactory<DataAuditClient> {
    /**
     * 获取Client对象
     *
     * @param vo
     * @return Client对象
     */
    @Override
    public DataAuditClient getFallBack(ResultVO vo) {
        return new DataAuditClient() {
            /**
             * 发布外部接口
             *
             * @param logDto 日志信息
             * @return 日志记录id
             */
            @Override
            public ResultVO<Integer> logRecord(DataAuditDTO logDto) {
                return null;
            }

        };
    }
}










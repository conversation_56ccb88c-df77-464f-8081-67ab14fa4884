package com.hvisions.log.capture.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.fallback.LogCaptureFallBackFacotry;
import com.hvisions.log.capture.common.dto.DataAuditDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <p>Title: DemoEntityController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "log-capture", fallbackFactory = LogCaptureFallBackFacotry.class)
public interface DataAuditClient {

    /**
     * 创建审计信息
     *
     * @param dto 审计信息
     * @return 调用结果
     */
    @ApiOperation(value = "创建审计信息")
    @PostMapping(value = "/create")
    ResultVO logRecord(@RequestBody DataAuditDTO dto);

}


























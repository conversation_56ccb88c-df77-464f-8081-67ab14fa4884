package com.hvisions.log.capture.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.fallback.LogCaptureFallBackFacotry;
import com.hvisions.log.capture.common.interfaces.ILog;
import com.hvisions.log.capture.common.dto.LogDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>Title: LogCaptureClient</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "log-capture",fallbackFactory = LogCaptureFallBackFacotry.class)
public interface LogCaptureClient extends ILog {

    /**
     * 发布外部接口
     *
     * @param logDto 日志信息
     * @return 日志记录id
     */
    @Override
    @PostMapping(value = "/log/logRecord")
    ResultVO<Integer> logRecord(@RequestBody LogDto logDto);

    /**
     * 发布外部接口
     *
     * @param logDto 日志信息
     * @return 日志记录id
     */
    @Override
    @PostMapping(value = "/log/piToMes")
    ResultVO<Integer> logRecordPiToMes(@RequestBody LogDto logDto);

    /**
     * 发布外部接口
     *
     * @param logDto 日志信息
     * @return 日志记录id
     */
    @Override
    @PostMapping(value = "/log/mesToPi")
    ResultVO<Integer> logRecordMesToPi(@RequestBody LogDto logDto);
}

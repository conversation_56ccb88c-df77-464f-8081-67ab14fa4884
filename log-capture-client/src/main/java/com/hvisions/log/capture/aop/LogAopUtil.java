package com.hvisions.log.capture.aop;

import cn.hutool.core.util.ReflectUtil;
import com.hvisions.common.consts.CookieConst;
import com.hvisions.common.consts.IntegerConst;
import com.hvisions.common.consts.RedisConst;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.log.capture.common.enums.LogTypeEnum;
import com.hvisions.log.capture.common.interfaces.ILog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <p>Title: LogAopUtil</p >
 * <p>Description: 日志记录Aop</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Aspect
@Slf4j
public class LogAopUtil {


    private final StringRedisTemplate stringRedisTemplate;
    private final ILog iLog;


    private static final ThreadLocal<LogDto> THREAD_LOCAL = new ThreadLocal<>();

    public LogAopUtil(StringRedisTemplate stringRedisTemplate, ILog iLog) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.iLog = iLog;
    }

    /**
     * 匹配包下的LogAnnotation注解
     */
    @Pointcut("execution(* com.hvisions..*.*Controller.*(..))")
    public void controllerMethodPointcut() {
    }

    /**
     * 微服务名称，如果没有配置，默认为服务名未配置
     */
    @Value("${h-visions.service-name:服务名未配置}")
    private String logModular;


    private void addLog(JoinPoint point) {
        LogDto logEntity = new LogDto();
        //将当前实体保存到threadLocal
        THREAD_LOCAL.set(logEntity);
        //塞入模块信息
        logEntity.setLogModular(logModular);
        //RequestContextHolder：持有上下文的Request容器,获取到当前请求的request
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        if (sra != null) {
            HttpServletRequest httpServletRequest = sra.getRequest();
            //获取sessionID存入实体类
            String token = httpServletRequest.getHeader(CookieConst.AUTH_TOKEN);
            //判断当前是否有token信息
            if (token != null) {
                String userIdStr = stringRedisTemplate.opsForValue()
                        .get(String.format(RedisConst.AUTH_REDIS_PREFIX, token));
                logEntity.setLogInvocation(userIdStr);
            }
            logEntity.setLocation(httpServletRequest.getRequestURI());
        }
        if (Strings.isBlank(logEntity.getLogInvocation())) {
            logEntity.setLogInvocation("0");
        }
        //这一步获取到的方法有可能是代理方法也有可能是真实方法
        Method m = ((MethodSignature) point.getSignature()).getMethod();
        //判断代理对象本身是否是连接点所在的目标对象，不是的话就要通过反射重新获取真实方法
        if (point.getThis().getClass() != point.getTarget().getClass()) {
            m = ReflectUtil.getMethod(point.getTarget().getClass(), m.getName(), m.getParameterTypes());
        }
        //通过真实方法获取该方法的参数名称
        LocalVariableTableParameterNameDiscoverer paramNames = new LocalVariableTableParameterNameDiscoverer();
        String[] parameterNames = paramNames.getParameterNames(m);
        //获取连接点方法运行时的入参列表
        Object[] args = point.getArgs();
        //将参数名称与入参值一一对应起来
        Map<String, Object> params = new HashMap<>(IntegerConst.MAP_SIZE);
        if (args != null && parameterNames != null) {
            if (args.length == parameterNames.length) {
                for (int i = 0; i < parameterNames.length; i++) {
                    params.put(parameterNames[i], args[i]);
                }
            }
        }
        //为log实体类的request字段赋值
        logEntity.setLogParameter(params.toString());
        logEntity.setMethodName(Optional.ofNullable(m.getAnnotation(ApiOperation.class))
                .map(ApiOperation::value).orElse(""));
        Annotation[] annotations = m.getDeclaringClass().getAnnotations();
        for (Annotation annotation : annotations) {
            if (annotation.annotationType().equals(Api.class)) {
                logEntity.setControllerName(((Api) annotation).description());
            }
        }
    }

    /**
     * 前置advice,再切面前进行
     *
     * @param point 切入点
     */
    @Before("controllerMethodPointcut()")
    public void before(JoinPoint point) {
        addLog(point);
    }

    /**
     * 方法成功return之后的advice
     *
     * @param point 切入点
     * @param rtv   返回对象
     */
    @AfterReturning(value = "controllerMethodPointcut()", returning = "rtv")
    public void after(JoinPoint point, Object rtv) {
        //得到当前线程的log对象
        LogDto logInfo = THREAD_LOCAL.get();
        //成功之后
        logInfo.setLogType(LogTypeEnum.SUCCESS.getCode());
        //插入一条log信息
        try {
            iLog.logRecord(logInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //移除当前log实体
        THREAD_LOCAL.remove();
    }

    /**
     * error 之后的advice
     *
     * @param throwing 异常
     */
    @AfterThrowing(value = "controllerMethodPointcut()", throwing = "throwing")
    public void error(Throwable throwing) {
        LogDto logInfo = THREAD_LOCAL.get();
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            //将报错信息写入error字段
            throwing.printStackTrace(new PrintStream(byteArrayOutputStream));
            logInfo.setLogExceptionMessage(byteArrayOutputStream.toString());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        //写入错误类型
        logInfo.setLogType(LogTypeEnum.ERROR.getCode());
        //添加日志记录  并移除当前log实体;

        try {
            iLog.logRecord(logInfo);
        } catch (Exception a) {
            log.error(a.getMessage(), a);
        }
        //移除当前log实体
        THREAD_LOCAL.remove();
    }


}

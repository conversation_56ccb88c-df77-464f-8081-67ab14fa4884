package com.hvisions.rawmaterial.dto.silotransfer;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 筒仓倒仓任务记录DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓倒仓任务记录DTO
 * @Date: 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "筒仓倒仓任务记录DTO")
public class SiloTransferRecordDTO {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "业务系统：ZHONGLIANG-中粮，BULER-布勒，JIESAI-捷赛", required = true)
    @NotBlank(message = "业务系统不能为空")
    private String businessSystem;

    @ApiModelProperty(value = "物料类型：SORGHUM-高粱，RICE_HUSK-稻壳")
    private String materialType;

    @ApiModelProperty(value = "任务类型", required = true)
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    @ApiModelProperty(value = "数据类型：TASK-任务，REALTIME-实时数据")
    private String dataType;

    @ApiModelProperty(value = "任务号", required = true)
    @NotBlank(message = "任务号不能为空")
    private String taskNo;

    @ApiModelProperty(value = "倒仓前筒仓号（源筒仓）")
    private String sourceSiloNo;

    @ApiModelProperty(value = "倒仓前筒仓名称（源筒仓）")
    private String sourceSiloName;

    @ApiModelProperty(value = "倒仓后筒仓号（目标筒仓）")
    private String targetSiloNo;

    @ApiModelProperty(value = "倒仓后筒仓名称（目标筒仓）")
    private String targetSiloName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "倒仓重量")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "计划重量")
    private BigDecimal plannedWeight;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "状态：1-待执行，2-执行中，3-已完成，4-失败，5-已取消")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "执行人ID")
    private Integer executorId;

    @ApiModelProperty(value = "执行人姓名")
    private String executorName;

    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completionTime;

    @ApiModelProperty(value = "中控系统任务ID")
    private String centralControlTaskId;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "车间名称")
    private String workshopName;

    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "中心名称")
    private String centerName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "数据来源")
    private String dataSource;

    @ApiModelProperty(value = "唯一标识用于去重")
    private String uniqueId;

    @ApiModelProperty(value = "倒仓进度百分比")
    private BigDecimal progressPercentage;

    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "创建人ID")
    private Integer creatorId;

    @ApiModelProperty(value = "更新人ID")
    private Integer updaterId;

    @ApiModelProperty(value = "持续时间（分钟）")
    private Long durationMinutes;

    @ApiModelProperty(value = "倒仓效率（kg/min）")
    private BigDecimal transferEfficiency;
}

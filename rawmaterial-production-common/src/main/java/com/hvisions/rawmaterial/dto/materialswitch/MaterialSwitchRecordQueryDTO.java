package com.hvisions.rawmaterial.dto.materialswitch;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 筒仓物料切换记录查询DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓物料切换记录查询DTO
 * @Date: 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "筒仓物料切换记录查询DTO")
public class MaterialSwitchRecordQueryDTO extends PageInfo {

    @ApiModelProperty(value = "业务系统列表：ZHONGLIANG-中粮，BULER-布勒，JIESAI-捷赛")
    private List<String> businessSystems;

    @ApiModelProperty(value = "物料类型列表：SORGHUM-高粱，RICE_HUSK-稻壳")
    private List<String> materialTypes;

    @ApiModelProperty(value = "任务类型")
    private String taskType;

    @ApiModelProperty(value = "数据类型：TASK-任务，REALTIME-实时数据")
    private String dataType;

    @ApiModelProperty(value = "任务号")
    private String taskNo;

    @ApiModelProperty(value = "筒仓号")
    private String siloNo;

    @ApiModelProperty(value = "筒仓名称")
    private String siloName;

    @ApiModelProperty(value = "更换前物料类型")
    private String beforeMaterialType;

    @ApiModelProperty(value = "更换前物料编码")
    private String beforeMaterialCode;

    @ApiModelProperty(value = "更换后物料类型")
    private String afterMaterialType;

    @ApiModelProperty(value = "更换后物料编码")
    private String afterMaterialCode;

    @ApiModelProperty(value = "状态列表：1-进行中，2-已完成，3-失败")
    private List<Integer> statuses;

    @ApiModelProperty(value = "执行人姓名")
    private String executorName;

    @ApiModelProperty(value = "中控系统任务ID")
    private String centralControlTaskId;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "切换时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date switchTimeStart;

    @ApiModelProperty(value = "切换时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date switchTimeEnd;

    @ApiModelProperty(value = "完成时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completionTimeStart;

    @ApiModelProperty(value = "完成时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completionTimeEnd;

    @ApiModelProperty(value = "创建时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    @ApiModelProperty(value = "数据来源")
    private String dataSource;

    @ApiModelProperty(value = "唯一标识")
    private String uniqueId;
}

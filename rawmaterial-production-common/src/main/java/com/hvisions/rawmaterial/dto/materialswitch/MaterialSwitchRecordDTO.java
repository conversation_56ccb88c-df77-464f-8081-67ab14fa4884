package com.hvisions.rawmaterial.dto.materialswitch;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 筒仓物料切换记录DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓物料切换记录DTO
 * @Date: 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "筒仓物料切换记录DTO")
public class MaterialSwitchRecordDTO {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "业务系统：ZHONGLIANG-中粮，BULER-布勒，JIESAI-捷赛", required = true)
    @NotBlank(message = "业务系统不能为空")
    private String businessSystem;

    @ApiModelProperty(value = "物料类型：SORGHUM-高粱，RICE_HUSK-稻壳")
    private String materialType;

    @ApiModelProperty(value = "任务类型", required = true)
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    @ApiModelProperty(value = "数据类型：TASK-任务，REALTIME-实时数据")
    private String dataType;

    @ApiModelProperty(value = "任务号", required = true)
    @NotBlank(message = "任务号不能为空")
    private String taskNo;

    @ApiModelProperty(value = "筒仓号")
    private String siloNo;

    @ApiModelProperty(value = "筒仓名称")
    private String siloName;

    @ApiModelProperty(value = "更换前物料类型")
    private String beforeMaterialType;

    @ApiModelProperty(value = "更换前物料编码")
    private String beforeMaterialCode;

    @ApiModelProperty(value = "更换前物料名称")
    private String beforeMaterialName;

    @ApiModelProperty(value = "更换后物料类型")
    private String afterMaterialType;

    @ApiModelProperty(value = "更换后物料编码")
    private String afterMaterialCode;

    @ApiModelProperty(value = "更换后物料名称")
    private String afterMaterialName;

    @ApiModelProperty(value = "切换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date switchTime;

    @ApiModelProperty(value = "状态：1-进行中，2-已完成，3-失败")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "执行人ID")
    private Integer executorId;

    @ApiModelProperty(value = "执行人姓名")
    private String executorName;

    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completionTime;

    @ApiModelProperty(value = "中控系统任务ID")
    private String centralControlTaskId;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "车间名称")
    private String workshopName;

    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "中心名称")
    private String centerName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "数据来源")
    private String dataSource;

    @ApiModelProperty(value = "唯一标识用于去重")
    private String uniqueId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "创建人ID")
    private Integer creatorId;

    @ApiModelProperty(value = "更新人ID")
    private Integer updaterId;
}

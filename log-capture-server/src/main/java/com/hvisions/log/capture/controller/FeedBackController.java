package com.hvisions.log.capture.controller;

import com.hvisions.log.capture.common.dto.FeedBackDTO;
import com.hvisions.log.capture.service.FeedBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: FeedBackController</p>
 * <p>Description: 反馈信息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021-8-6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/feedBack")
@Api(description = "反馈信息控制器")
public class FeedBackController {

    private final FeedBackService feedBackService;

    @Autowired
    public FeedBackController(FeedBackService feedBackService) {
        this.feedBackService = feedBackService;
    }

    /**
     * 添加反馈信息
     *
     * @param feedBackDTO FeedBack
     */
    @ApiOperation(value = "添加FeedBack信息")
    @PostMapping(value = "/add")
    public void addFeedBack(@Valid @RequestBody FeedBackDTO feedBackDTO) {
        feedBackService.addFeedBack(feedBackDTO);
    }


    /**
     * 查询全部
     *
     * @return 反馈信息列表
     */
    @ApiOperation(value = "获取FeedBack列表")
    @GetMapping(value = "/getAll")
    public List<FeedBackDTO> getAll() {
        return feedBackService.getAll();
    }
}
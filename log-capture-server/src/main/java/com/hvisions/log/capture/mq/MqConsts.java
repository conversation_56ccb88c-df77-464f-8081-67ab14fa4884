package com.hvisions.log.capture.mq;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: PiMqConsts
 * @description:
 * @date 2025/5/27 9:55
 */
public interface MqConsts {
    String PI_EXCHANGE = "pi-exchange";
    String PI_EXCHANGE_V2 = "h-visions.pi.exchange.v2";
    String PI_SIMPLE_TOPIC = "pi.simple";

    String LOG_EXCHANGE = "log-logRecord-log-capture-server";
    String LOG_SIMPLE_TOPIC = "addLogCapture";

    String LOG_PI_EXCHANGE = "log-logRecordPiToMes-log-capture-server";
    String LOG_PI_SIMPLE_TOPIC = "piToMesLog";
}

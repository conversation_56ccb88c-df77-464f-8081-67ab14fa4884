package com.hvisions.log.capture.service;

import com.hvisions.common.service.BaseService;
import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import com.hvisions.log.capture.entity.HvBmLogCapture;
import org.springframework.data.domain.Page;

/**
 * <p>Title: LogCaptureService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface LogCaptureService {
    /**
     * 记录调用信息
     *
     * @param logCaptureDTO 日志
     * @return 插入记录的ID
     */
    Integer logRecord(LogCaptureDTO logCaptureDTO);


    /**
     * 删除Id列表删除数据;
     *
     * @param day 删除
     */

    void deleteByLogCaptureTime(int day);

    /**
     * 条件查询
     *
     * @param logCaptureDTO 查询条件传输对象
     * @return 日志记录信息
     */
    Page<LogCaptureDTO> findLogByLogTypeOrLogInvocationOrLogModular(LogQueryDTO logCaptureDTO);


    /**
     * 条件查询
     *
     * @param logQueryDTO 查询条件传输对象
     * @return 日志记录信息
     */
    Page<LogCaptureDTO> getLogByQuery(LogQueryDTO logQueryDTO);

    /**
     * 删除日志
     * @param id 日志id
     */
    void delete(Integer id);

    void deleteByLogMesToPi(int day);

    void deleteByLogPiToMes(int day);
}

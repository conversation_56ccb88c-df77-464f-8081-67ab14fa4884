package com.hvisions.log.capture.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import com.hvisions.log.capture.common.enums.LogTypeEnum;
import com.hvisions.log.capture.service.LogCaptureService;
import com.hvisions.log.capture.service.LogMesPiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <p>Title: DemoEntityController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/log")
@Slf4j
@Api(description = "接口日志控制器")

public class LogCaptureController {
    private final LogCaptureService logCaptureService;

    private final LogMesPiService logMesPiService;

    @Autowired
    public LogCaptureController(LogCaptureService logCaptureService, LogMesPiService logMesPiService) {
        this.logCaptureService = logCaptureService;
        this.logMesPiService = logMesPiService;
    }

    /**
     * 加入日志记录调用信息
     *
     * @param logCaptureDTO 传入的对象
     */
    @ApiOperation(value = "加入日志记录调用信息")
    @PostMapping(value = "/logRecord")
    @Async
    public void logRecord(@RequestBody LogCaptureDTO logCaptureDTO) {
        logCaptureService.logRecord(logCaptureDTO);
    }

    /**
     * 加入日志记录调用信息
     *
     * @param logCaptureDTO 传入的对象
     */
    @ApiOperation(value = "加入日志记录调用信息-piToMes")
    @PostMapping(value = "/piToMes")
    @Async
    public void logRecordPiToMes(@RequestBody LogCaptureDTO logCaptureDTO) {
        logMesPiService.logRecordPiToMes(logCaptureDTO);
    }

    /**
     * 加入日志记录调用信息
     *
     * @param logCaptureDTO 传入的对象
     */
    @ApiOperation(value = "加入日志记录调用信息-mesToPi")
    @PostMapping(value = "/mesToPi")
    @Async
    public void logRecordMesToPi(@RequestBody LogCaptureDTO logCaptureDTO) {
        logMesPiService.logRecordMesToPi(logCaptureDTO);
    }

    /**
     * 删除
     *
     * @param id 实体id
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/deleteLogCapture/{id}")
    public void deleteLogCapture(@PathVariable Integer id) {
        logCaptureService.delete(id);
    }


    @Deprecated
    @ApiOperation(value = "分页查询")
    @RequestMapping(value = "/findLogByLogTypeOrLogInvocationOrLogModular", method = RequestMethod.POST)
    public Page<LogCaptureDTO> findLogByLogTypeOrLogInvocationOrLogModular(@RequestBody LogQueryDTO logCaptureDTO) {
        return logCaptureService.findLogByLogTypeOrLogInvocationOrLogModular(logCaptureDTO);
    }

    /**
     * 分页查询日志信息
     *
     * @param logQueryDTO 查询条件
     * @return 日志分页信息
     */
    @ApiOperation(value = "分页查询")
    @RequestMapping(value = "/getLogByQuery", method = RequestMethod.POST)
    public Page<LogCaptureDTO> getLogByQuery(@RequestBody LogQueryDTO logQueryDTO) {
        return logCaptureService.getLogByQuery(logQueryDTO);
    }

    /**
     * 查询所有的日志类型
     *
     * @return 日志类型枚举信息
     */
    @ApiOperation(value = "查询日志类型")
    @GetMapping(value = "/getLogType")
    public Map<Integer, String> getLogType() {
        return EnumUtil.enumToMap(LogTypeEnum.class);
    }

}


























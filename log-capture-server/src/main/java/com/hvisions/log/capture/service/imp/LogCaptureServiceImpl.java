package com.hvisions.log.capture.service.imp;

import com.github.pagehelper.PageHelper;
import com.hvisions.auth.client.AuthClient;
import com.hvisions.auth.dto.user.UserInfoDto;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import com.hvisions.log.capture.dao.LogCaptureMapper;
import com.hvisions.log.capture.entity.HvBmLogCapture;
import com.hvisions.log.capture.repository.LogCaptureRepository;
import com.hvisions.log.capture.repository.LogMesToPiRepository;
import com.hvisions.log.capture.repository.LogPiToMesRepository;
import com.hvisions.log.capture.service.LogCaptureService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: DemoEntityServiceImp</p>
 * <p>Description: 服务实现层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class LogCaptureServiceImpl implements LogCaptureService {

    private final LogCaptureRepository logCaptureRepository;
    private final AuthClient authClient;
    private final LogCaptureMapper captureMapper;

    private final LogMesToPiRepository logMesToPiRepository;
    private final LogPiToMesRepository logPiToMesRepository;

    @Autowired
    public LogCaptureServiceImpl(LogCaptureRepository logCaptureRepository, LogMesToPiRepository logMesToPiRepository, LogPiToMesRepository logPiToMesRepository, AuthClient authClient, LogCaptureMapper captureMapper) {
        //调用父类的构造函数，传入jpa对象，实现普通的增删改查功能
        this.logCaptureRepository = logCaptureRepository;
        this.logMesToPiRepository = logMesToPiRepository;
        this.logPiToMesRepository = logPiToMesRepository;
        this.authClient = authClient;
        this.captureMapper = captureMapper;
    }

    /**
     * 添加日志
     *
     * @param logCaptureDTO dto
     * @return id
     */
    @Override
    public Integer logRecord(LogCaptureDTO logCaptureDTO) {
        return logCaptureRepository.save(DtoMapper.convert(logCaptureDTO, HvBmLogCapture.class)).getId();
    }

    /**
     * 根据ID列表删除数据
     *
     * @param day 清楚的之前天数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByLogCaptureTime(int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_MONTH, -day);
        logCaptureRepository.deleteByLogCaptureTime(cal.getTime());
    }


    /**
     * 分页查询
     *
     * @param logCaptureDTO 日志查询DTO
     * @return dto
     */
    @Override
    public Page<LogCaptureDTO> findLogByLogTypeOrLogInvocationOrLogModular(LogQueryDTO logCaptureDTO) {
        Page<HvBmLogCapture> hvBmLogCaptures;
        HvBmLogCapture logCapture = DtoMapper.convert(logCaptureDTO, HvBmLogCapture.class);
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
            .withMatcher("logType", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
            .withMatcher("logInvocation", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
            .withMatcher("logModular", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
            .withIgnorePaths("logCaptureTime");

        Example<HvBmLogCapture> example = Example.of(logCapture, exampleMatcher);
        hvBmLogCaptures = logCaptureRepository.findAll(example, logCaptureDTO.getRequest());
        //分页信息
        Page<LogCaptureDTO> dtos = DtoMapper.convertPage(hvBmLogCaptures, logCaptureDTO.getRequest(), LogCaptureDTO.class);
        //分页中 调用人的信息ID
        List<String> userIds = dtos.stream().map(LogDto::getLogInvocation).collect(Collectors.toList());
        //类型转换
        List<Integer> userIdList = new ArrayList<>();
        for (String id : userIds) {
            try {
                userIdList.add(Integer.parseInt(id));
            } catch (Exception ignored) {

            }
        }
        //查询出的调用人信息
        ResultVO<List<UserInfoDto>> userInfoByIds = authClient.getUserInfoByIds(userIdList);
        //合并调用人名称到分页信息中
        if (userInfoByIds.isSuccess()) {
            for (LogCaptureDTO logCaptureDTO1 : dtos) {
                Optional<UserInfoDto> userInfoDtoByInvocation = userInfoByIds
                    .getData().stream().filter(t -> t.getId().toString().equals(logCaptureDTO1.getLogInvocation())).findFirst();
                userInfoDtoByInvocation.ifPresent(userInfoDto -> logCaptureDTO1.setLogInvocationName(userInfoDtoByInvocation.get().getUserName()));
            }
        }

        return dtos;
    }

    /**
     * 条件查询
     *
     * @param logQueryDTO 查询条件传输对象
     * @return 日志记录信息
     */
    @Override
    public Page<LogCaptureDTO> getLogByQuery(LogQueryDTO logQueryDTO) {
        if(StringUtils.isNotEmpty(logQueryDTO.getLogModular()) || StringUtils.isNotEmpty(logQueryDTO.getLogInvocation()) ||StringUtils.isNotEmpty(logQueryDTO.getMethodName())
            ||StringUtils.isNotEmpty(logQueryDTO.getControllerName()) || logQueryDTO.getStartTime() != null){
            return PageHelperUtil.getPage(captureMapper::getLogByQuery, logQueryDTO);
        }
        //计算查询总数
        Long max = captureMapper.getLogMaxId(logQueryDTO);
        Long min = captureMapper.getLogMinId(logQueryDTO);
        Long total = max - min;
        setparam(logQueryDTO);
        //查询具体的数据
        List<LogCaptureDTO> result = captureMapper.getLogByQuery(logQueryDTO);
        PageImpl page = new PageImpl(result, logQueryDTO.getRequest(), total);
        return page;
    }

    private static void setparam(LogQueryDTO param) {
        PageHelper.startPage(param.getRequest().getPageNumber() + 1, param.getRequest().getPageSize(), false);
        if (param.getRequest().getSort().isSorted()) {
            StringBuilder orderBy = new StringBuilder();

            for(Iterator<Sort.Order> iterator = param.getRequest().getSort().iterator(); iterator.hasNext(); orderBy.append(" ")) {
                Sort.Order next = (Sort.Order)iterator.next();
                String property = next.getProperty();
                orderBy.append(property);
                orderBy.append(" ");
                if (next.getDirection().isAscending()) {
                    orderBy.append("ASC");
                } else {
                    orderBy.append("DESC");
                }
            }

            PageHelper.orderBy(orderBy.toString());
        }
    }

    /**
     * 删除日志
     *
     * @param id 日志id
     */
    @Override
    public void delete(Integer id) {
        logCaptureRepository.deleteById(id);
    }

    @Override
    public void deleteByLogMesToPi(int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_MONTH, -day);
        logMesToPiRepository.deleteByLogMesToPi(cal.getTime());
    }

    @Override
    public void deleteByLogPiToMes(int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_MONTH, -day);
        logPiToMesRepository.deleteByLogPiToMes(cal.getTime());
    }


}


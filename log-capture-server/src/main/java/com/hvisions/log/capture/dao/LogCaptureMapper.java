package com.hvisions.log.capture.dao;

import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title: logCaptureMapper</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/10/30</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Mapper
public interface LogCaptureMapper {


    /**
     * 查询日志记录
     *
     * @param logQueryDTO 查询条件
     * @return 返回结果
     */
    List<LogCaptureDTO> getLogByQuery(@Param("dto") LogQueryDTO logQueryDTO);

    Long getLogMaxId(@Param("dto") LogQueryDTO logQueryDTO);

    Long getLogMinId(@Param("dto") LogQueryDTO logQueryDTO);
}
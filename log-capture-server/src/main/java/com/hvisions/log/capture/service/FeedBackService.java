package com.hvisions.log.capture.service;

import com.hvisions.log.capture.common.dto.FeedBackDTO;

import java.util.List;

/**
 * <p>Title: FeedBackService</p>
 * <p>Description: 反馈信息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021-8-6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

public interface FeedBackService {
    /**
     * 保存反馈信息
     *
     * @param feedBackDTO FeedBack
     */
    void addFeedBack(FeedBackDTO feedBackDTO);

    /**
     * 通过id删除反馈信息
     *
     * @param id 主键
     */
    void deleteFeedBack(Integer id);

    /**
     * 修改反馈信息
     *
     * @param feedBackDTO FeedBack
     */
    void updateFeedBack(FeedBackDTO feedBackDTO);

    /**
     * 获取反馈信息
     *
     * @param id 主键
     * @return FeedBack feedBackDTO FeedBack
     */
    FeedBackDTO getFeedBackById(Integer id);

    /**
     * 获取反馈信息列表
     *
     * @return FeedBack列表
     */
    List<FeedBackDTO> getAll();
}
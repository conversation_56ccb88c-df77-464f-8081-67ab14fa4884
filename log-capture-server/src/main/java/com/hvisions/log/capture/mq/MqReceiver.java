package com.hvisions.log.capture.mq;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.service.LogCaptureService;
import com.hvisions.log.capture.service.LogMesPiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.RabbitListenerErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;


/**
 * MQ消费
 */
@Configuration
@Slf4j
public class MqReceiver {

    @Resource
    private LogMesPiService logMesPiService;

    @Resource
    private LogCaptureService logCaptureService;


    /**
     * 监听队列,pi传输的数据添加日志
     *
     * @param josnStr 传输的数据
     */
    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConsts.LOG_PI_SIMPLE_TOPIC, autoDelete = "false"),
            exchange = @Exchange(value = MqConsts.LOG_PI_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = MqConsts.LOG_PI_SIMPLE_TOPIC), errorHandler = "fixErrorHandler")
    public void processPiToMes(String josnStr) {
        LogCaptureDTO logCaptureDTO = JSONObject.parseObject(josnStr, LogCaptureDTO.class);
        logMesPiService.logRecordPiToMes(logCaptureDTO);

    }

    /**
     * 监听队列,其他传输的数据添加日志
     *
     * @param josnStr 传输的数据
     */
    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConsts.LOG_SIMPLE_TOPIC, autoDelete = "false"),
            exchange = @Exchange(value = MqConsts.LOG_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = MqConsts.LOG_SIMPLE_TOPIC), errorHandler = "fixErrorHandler")
    public void process(String josnStr) {
        LogCaptureDTO logCaptureDTO = JSONObject.parseObject(josnStr, LogCaptureDTO.class);
        logCaptureService.logRecord(logCaptureDTO);

    }

    @Bean
    public RabbitListenerErrorHandler fixErrorHandler() {
        return (message, message1, e) -> {
            log.error(e.getMessage(), e);
            return null;
        };
    }
}

package com.hvisions.log.capture.configuration;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description:话题模式消息队列 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class TopicRabbitConfig {
    /**
     * 消息队列Queue名称
     */
    final static String LOG_QUEUE = "h-visions.syslog.queue";
    private final static String LOG_EXCHANGE = "h-visions.syslog";


    @Bean
    public Queue logQueue() {
        return new Queue(TopicRabbitConfig.LOG_QUEUE);
    }

    @Bean
    TopicExchange exchange() {
        return new TopicExchange(LOG_EXCHANGE);
    }

    /**
     * 绑定队列和关注的消息
     *
     * @param logQueue 队列
     * @param exchange 分发器
     * @return 绑定
     */
    @Bean
    Binding bindingExchangeMessage(Queue logQueue, TopicExchange exchange) {
        return BindingBuilder.bind(logQueue).to(exchange).with("*.*");
    }


}

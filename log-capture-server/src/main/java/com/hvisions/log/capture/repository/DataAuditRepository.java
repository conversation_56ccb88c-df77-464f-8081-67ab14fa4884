package com.hvisions.log.capture.repository;

import com.hvisions.log.capture.entity.HvBmDataAudit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: DataAuditRepository</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/11/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface DataAuditRepository extends JpaRepository<HvBmDataAudit, Long> {
    /**
     * 清除过期日志
     *
     * @param time 结束时间
     */
    @Modifying
    @Query(value = "delete FROM HvBmDataAudit l where  l.updateTime < ?1")
    void deleteByAuditTime(Date time);

    /**
     * 查询实体的变更记录
     *
     * @param serviceName 服务名称
     * @param entity      实体名
     * @param entityId    实体主键
     * @return 实体的历史记录
     */
    @Query(value = "SELECT * FROM hv_bm_data_audit WHERE service_name = ?1 AND entity = ?2 AND entity_id = ?3 ORDER BY update_time",nativeQuery = true)
    List<HvBmDataAudit> findHistory(String serviceName, String entity, String entityId);
}










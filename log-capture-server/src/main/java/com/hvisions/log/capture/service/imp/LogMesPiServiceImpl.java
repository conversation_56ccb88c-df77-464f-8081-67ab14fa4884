package com.hvisions.log.capture.service.imp;

import com.github.pagehelper.PageHelper;
import com.hvisions.auth.client.AuthClient;
import com.hvisions.auth.dto.user.UserInfoDto;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import com.hvisions.log.capture.dao.LogCaptureMapper;
import com.hvisions.log.capture.entity.HvBmLogCapture;
import com.hvisions.log.capture.entity.HvBmLogMesToPi;
import com.hvisions.log.capture.entity.HvBmLogPiToMes;
import com.hvisions.log.capture.repository.LogCaptureRepository;
import com.hvisions.log.capture.repository.LogMesToPiRepository;
import com.hvisions.log.capture.repository.LogPiToMesRepository;
import com.hvisions.log.capture.service.LogCaptureService;
import com.hvisions.log.capture.service.LogMesPiService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class LogMesPiServiceImpl implements LogMesPiService {

    private final LogMesToPiRepository logMesToPiRepository;
    private final LogPiToMesRepository logPiToMesRepository;

    @Autowired
    public LogMesPiServiceImpl(LogMesToPiRepository logMesToPiRepository, LogPiToMesRepository logPiToMesRepository) {
        //调用父类的构造函数，传入jpa对象，实现普通的增删改查功能
        this.logMesToPiRepository = logMesToPiRepository;
        this.logPiToMesRepository = logPiToMesRepository;
    }

    /**
     * 添加日志
     *
     * @param logCaptureDTO dto
     * @return id
     */
    @Override
    public Integer logRecordPiToMes(LogCaptureDTO logCaptureDTO) {
        return logPiToMesRepository.save(DtoMapper.convert(logCaptureDTO, HvBmLogPiToMes.class)).getId();
    }

    @Override
    public Integer logRecordMesToPi(LogCaptureDTO logCaptureDTO) {
        return logMesToPiRepository.save(DtoMapper.convert(logCaptureDTO, HvBmLogMesToPi.class)).getId();
    }
}


package com.hvisions.log.capture.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: LogCaptureExceptionEnum</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/9/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum LogCaptureExceptionEnum implements BaseErrorCode {
    /**
     * 异常类型
     */
    DEMO_EXCEPTION_ENUM(30001),
    USER_ID_IS_NULL(30002),
    ;
    private Integer code;

    LogCaptureExceptionEnum(int code) {
        this.code = code;
    }


    @Override
    public String getMessage() {
        return this.toString();
    }
}

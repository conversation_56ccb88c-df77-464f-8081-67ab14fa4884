package com.hvisions.log.capture.controller;

import com.hvisions.log.capture.common.dto.DataAuditDTO;
import com.hvisions.log.capture.common.dto.DataAudtiQuery;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import com.hvisions.log.capture.service.DataAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>Title: DemoEntityController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/dataAudit")
@Slf4j
@Api(description = "数据审计控制器")

public class DataAuditController {
    private final DataAuditService dataAuditService;

    @Autowired
    public DataAuditController(DataAuditService dataAuditService) {
        this.dataAuditService = dataAuditService;
    }


    /**
     * 创建审计信息
     *
     * @param dto 审计信息
     */
    @ApiOperation(value = "创建审计信息")
    @PostMapping(value = "/create")
    public void create(@RequestBody DataAuditDTO dto) {
        dataAuditService.createAudit(dto);
    }


    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/findPage")
    public Page<DataAuditDTO> findPage(@RequestBody DataAudtiQuery query) {
        return dataAuditService.getPage(query);
    }


    /**
     * 查询实体的更改日志
     *
     * @param serviceName 服务名
     * @param entity      实体
     * @param entityId    实体主键
     * @return 实体更新日志
     */
    @ApiOperation(value = "/查询实体历史")
    @GetMapping(value = "/findHistory")
    public List<DataAuditDTO> findHistory(@RequestParam String serviceName,
                                          @RequestParam String entity,
                                          @RequestParam String entityId) {
        return dataAuditService.getHistory(serviceName, entity, entityId);
    }

}


























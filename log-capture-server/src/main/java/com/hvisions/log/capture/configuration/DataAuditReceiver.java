package com.hvisions.log.capture.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.log.capture.common.dto.DataAuditDTO;
import com.hvisions.log.capture.service.DataAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.listener.exception.ListenerExecutionFailedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <p>Title: TopicReceiver</p>
 * <p>Description: TopicReceiver</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class DataAuditReceiver {
    private final DataAuditService dataAuditService;
    private final ObjectMapper objectMapper;

    @Autowired
    public DataAuditReceiver(DataAuditService dataAuditService, ObjectMapper objectMapper) {
        this.dataAuditService = dataAuditService;
        this.objectMapper = objectMapper;
    }


    @Bean
    public RabbitListenerErrorHandler auditErrorHandler() {
        return new RabbitListenerErrorHandler() {
            @Override
            public Object handleError(Message message, org.springframework.messaging.Message<?> message1, ListenerExecutionFailedException e) throws Exception {
                log.error("log insert error:" + message.toString(), e);
                return null;
            }
        };
    }

    /**
     * 监听队列
     *
     * @param json 审计信息
     */
    @RabbitListener(queues = DataAuditRabbitConfig.LOG_QUEUE, errorHandler = "auditErrorHandler")
    public void process(String json) throws IOException {
        DataAuditDTO dto = objectMapper.readValue(json, DataAuditDTO.class);
        dataAuditService.createAudit(dto);
    }

}










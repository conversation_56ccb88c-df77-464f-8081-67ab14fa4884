package com.hvisions.log.capture.configuration;

import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import java.time.Duration;

/**
 * redis配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class RedisConfig {

	@Autowired
	private RedisClusterProperties clusterProperties;

	@Bean
	public LettuceConnectionFactory redisConnectionFactory() {
		// 1. 动态构建集群配置
		RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(clusterProperties.getNodes());

		// 设置密码（需要转换为 RedisPassword 类型）
		if (clusterProperties.getPassword() != null && !clusterProperties.getPassword().isEmpty()) {
			clusterConfig.setPassword(RedisPassword.of(clusterProperties.getPassword()));
		}

		// 2. 拓扑刷新配置（保持原有优化）
		ClusterTopologyRefreshOptions refreshOptions = ClusterTopologyRefreshOptions.builder()
				.enablePeriodicRefresh(Duration.ofSeconds(3))
				.enableAllAdaptiveRefreshTriggers()
				.adaptiveRefreshTriggersTimeout(Duration.ofSeconds(10))
				.build();

		// 3. 客户端配置
		LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
				.commandTimeout(Duration.ofSeconds(2))
				.clientOptions(ClusterClientOptions.builder()
						.topologyRefreshOptions(refreshOptions)
						.validateClusterNodeMembership(false)
						.build())
				.build();

		return new LettuceConnectionFactory(clusterConfig, clientConfig);
	}
}
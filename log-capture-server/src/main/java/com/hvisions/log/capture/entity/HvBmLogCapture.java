package com.hvisions.log.capture.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>Title: HvBmLogCapture</p>
 * <p>Description: 行政部门表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Entity
@Data
public class HvBmLogCapture extends SysBase{

    /**
     * 类型
     */
    private Integer logType;
    /**
     * 调用时间
     */
    private Date logCaptureTime = new Date();
    /**
     * 调用传参
     */
    @Lob
    private String logParameter;
    /**
     * 模块（调用的微服务的名称）
     */
    private String logModular;
    /**
     * 异常信息
     */
    @Lob
    private String logExceptionMessage;
    /**
     * 调用人
     */
    private String logInvocation;
    /**
     * 调用的API名称和路径
     */
    private String location;
    /**
     * 控制器名称
     */
    private String controllerName;
    /**
     * 调用的方法名称
     */
    private String methodName;

}

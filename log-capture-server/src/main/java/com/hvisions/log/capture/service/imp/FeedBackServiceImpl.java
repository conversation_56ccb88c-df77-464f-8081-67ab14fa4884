package com.hvisions.log.capture.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.log.capture.common.dto.FeedBackDTO;
import com.hvisions.log.capture.entity.HvBmLogFeedBack;
import com.hvisions.log.capture.repository.FeedBackRepository;
import com.hvisions.log.capture.service.FeedBackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <p>Title: FeedBackServiceImpl</p>
 * <p>Description: 反馈信息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021-8-6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Service
public class FeedBackServiceImpl implements FeedBackService {

    private final FeedBackRepository feedBackRepository;
    @Value("${spring.mail.username}")
    private String mailFrom;
    @Value("${h-visions.feedback.mail}")
    private String activeUserMail;
    private final JavaMailSender mailSender;

    @Autowired
    public FeedBackServiceImpl(FeedBackRepository feedBackRepository, JavaMailSender mailSender) {
        this.feedBackRepository = feedBackRepository;
        this.mailSender = mailSender;
    }

    /**
     * 保存反馈信息
     *
     * @param feedBackDTO FeedBack
     */
    @Override
    public void addFeedBack(FeedBackDTO feedBackDTO) {
        HvBmLogFeedBack entity = convert(feedBackDTO);
        feedBackRepository.save(entity);
        //发送邮件
        sendMessage(feedBackDTO);
    }


    /**
     * 通过id删除反馈信息
     *
     * @param id 主键
     */
    @Override
    public void deleteFeedBack(Integer id) {
        feedBackRepository.deleteById(id);
    }

    /**
     * 修改反馈信息
     *
     * @param feedBackDTO FeedBack
     */
    @Override
    public void updateFeedBack(FeedBackDTO feedBackDTO) {
        feedBackRepository.save(DtoMapper.convert(feedBackDTO, HvBmLogFeedBack.class));
    }

    /**
     * 获取反馈信息
     *
     * @param id 主键
     * @return FeedBack feedBackDTO FeedBack
     */
    @Override
    public FeedBackDTO getFeedBackById(Integer id) {
        Optional<HvBmLogFeedBack> optional = feedBackRepository.findById(id);
        return optional.map(feedBack -> DtoMapper.convert(feedBack, FeedBackDTO.class)).orElse(null);
    }

    /**
     * 获取反馈信息列表
     *
     * @return feedBackDTO 列表
     */
    @Override
    public List<FeedBackDTO> getAll() {
        return DtoMapper.convertList(feedBackRepository.findAll(), FeedBackDTO.class);
    }

    /**
     * 对象转换
     *
     * @param feedBackDTO 反馈信息
     * @return 数据库实体
     */
    private HvBmLogFeedBack convert(FeedBackDTO feedBackDTO) {
        HvBmLogFeedBack entity = DtoMapper.convert(feedBackDTO, HvBmLogFeedBack.class);
        entity.setDocument(String.join(",", feedBackDTO.getDocument().stream()
                .map(Object::toString).collect(Collectors.toList())));
        entity.setPicture(String.join(",", feedBackDTO.getPicture().stream()
                .map(Object::toString).collect(Collectors.toList())));
        return entity;
    }

    /**
     * 发送邮件
     *
     * @param feedBackDTO 反馈信息
     */
    private void sendMessage(FeedBackDTO feedBackDTO) {
        //发送邮件
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(activeUserMail);
        message.setFrom(mailFrom);
        message.setSubject(String.format("收到反馈信息,产品信息：%s", feedBackDTO.getProductNameVersion()));
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("产品名称以及版本：%s", feedBackDTO.getProductNameVersion()));
        stringBuilder.append("\r\n");
        stringBuilder.append(String.format("问题分类:%s", feedBackDTO.getType()));
        stringBuilder.append("\r\n");
        stringBuilder.append(String.format("问题描述:%s", feedBackDTO.getDescription()));
        stringBuilder.append("\r\n");
        stringBuilder.append(String.format("电子邮箱:%s", feedBackDTO.getEmail()));
        stringBuilder.append("\r\n");
        stringBuilder.append(String.format("用户名称:%s", feedBackDTO.getUserName()));
        message.setText(stringBuilder.toString());
        mailSender.send(message);
    }
}
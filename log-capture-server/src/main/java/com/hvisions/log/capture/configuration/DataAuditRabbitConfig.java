package com.hvisions.log.capture.configuration;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description: 数据审计日志 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class DataAuditRabbitConfig {
    /**
     * 消息队列Queue名称
     */
    final static String LOG_QUEUE = "h-visions.log.data.audit.queue";
    private final static String LOG_EXCHANGE = "h-visions.log.data.audit";


    @Bean
    public Queue dataAuditQueue() {
        return new Queue(DataAuditRabbitConfig.LOG_QUEUE);
    }

    @Bean
    TopicExchange dataAuditExchange() {
        return new TopicExchange(LOG_EXCHANGE);
    }

    /**
     * 绑定队列和关注的消息
     *
     * @param dataAuditQueue    队列
     * @param dataAuditExchange 分发器
     * @return 绑定
     */
    @Bean
    Binding bindingExchangeMessage1(Queue dataAuditQueue, TopicExchange dataAuditExchange) {
        return BindingBuilder.bind(dataAuditQueue).to(dataAuditExchange).with("*.*.*");
    }


}

package com.hvisions.log.capture.configuration;

import com.hvisions.common.advice.ControllerExceptionHandler;
import com.hvisions.common.advice.HvisionsApiResultHandler;
import com.hvisions.common.advice.ResultFactory;
import com.hvisions.common.component.HvisionsI18nInternational;
import com.hvisions.common.utils.HvisionsExceptionMapper;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <p>Title: AuthConfig</p>
 * <p>Description: 统一配置，可以重写其中的类来实现特定的策略</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class Config {
    @Value("${spring.messages.basename}")
    private String basename;

    @Value("${spring.messages.cache-seconds}")
    private long cacheMillis;

    @Value("${spring.messages.encoding}")
    private String encoding;

    /**
     * 国际化类
     */
    @Bean
    HvisionsI18nInternational getHVisionsI18nInternational() {
        return new HvisionsI18nInternational(basename, cacheMillis, encoding);
    }

    /**
     * 异常信息处理类
     */
    @Bean
    HvisionsExceptionMapper getHVisionsExceptionMapper() {
        return new HvisionsExceptionMapper();
    }

    /**
     * 异常信息处理类
     *
     * @param hVisionsI18nInternational hVisionsI18nInternational
     * @param hVisionsExceptionMapper   hVisionsExceptionMapper
     * @return ResultVOFactory
     */
    @Bean
    ResultFactory getResultVOFactory(HvisionsI18nInternational hVisionsI18nInternational,
                                     HvisionsExceptionMapper hVisionsExceptionMapper) {
        return new ResultFactory(hVisionsI18nInternational, hVisionsExceptionMapper);
    }

    /**
     * 全局统一异常处理
     */
    @Bean
    ControllerExceptionHandler getControllerExceptionHandler(ResultFactory resultVOFactory) {
        return new ControllerExceptionHandler(resultVOFactory);

    }

    /**
     * 全局统一结果处理类
     */
    @Bean
    HvisionsApiResultHandler getHVisionsApiResultHandler() {
        return new HvisionsApiResultHandler();
    }

    @Bean
    public DatabaseIdProvider getDatabaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.setProperty("Oracle", "oracle");
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("MS-SQL", "sqlserver");
        properties.setProperty("Microsoft SQL Server", "sqlserver");
        properties.setProperty("PostgreSQL", "postgresql");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }
}

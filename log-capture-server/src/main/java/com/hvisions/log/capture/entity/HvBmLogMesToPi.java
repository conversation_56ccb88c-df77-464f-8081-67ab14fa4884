package com.hvisions.log.capture.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "hv_bm_log_mes_to_pi")
@Entity
@TableName("hv_bm_log_mes_to_pi")
@KeySequence("hv_bm_log_mes_to_pi_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HvBmLogMesToPi extends SysBase{

    /**
     * 类型
     */
    @Column(columnDefinition = "int COMMENT '类型'")
    private Integer logType;
    /**
     * 调用时间
     */
    @Column(columnDefinition = "datetime COMMENT '调用时间'")
    private Date logCaptureTime = new Date();
    /**
     * 调用传参
     */
    @Lob
    @Column(columnDefinition = "longtext COMMENT '调用传参'")
    private String logParameter;
    /**
     * 模块（调用的微服务的名称）
     */
    @Column(columnDefinition = "varchar(255) COMMENT '模块（调用的微服务的名称）'")
    private String logModular;
    /**
     * 异常信息
     */
    @Lob
    @Column(columnDefinition = "longtext COMMENT '异常信息'")
    private String logExceptionMessage;
    /**
     * 调用人
     */
    @Column(columnDefinition = "varchar(255) COMMENT '调用人'")
    private String logInvocation;
    /**
     * 调用的API名称和路径
     */
    @Column(columnDefinition = "varchar(255) COMMENT '调用的API名称和路径'")
    private String location;
    /**
     * 控制器名称
     */
    @Column(columnDefinition = "varchar(255) COMMENT '控制器名称'")
    private String controllerName;
    /**
     * 调用的方法名称
     */
    @Column(columnDefinition = "varchar(255) COMMENT '调用的方法名称'")
    private String methodName;

}

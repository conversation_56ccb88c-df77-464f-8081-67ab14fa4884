package com.hvisions.log.capture.repository;

import com.hvisions.log.capture.entity.HvBmLogCapture;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <p>Title: DemoEntityRepository</p>
 * <p>Description: 系统行政部门仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface LogCaptureRepository extends JpaRepository<HvBmLogCapture, Integer> {


    /**
     * 根据传入条件查询
     *
     * @param logType    类型
     * @param logModular 微服务名称
     * @param location   API名称 位置
     * @param pageable   分页
     * @return 分页信息
     */
    Page<HvBmLogCapture> findByLogTypeContainsAndLogModularContainsAndLocation(String logType, String logModular, String location, Pageable pageable);

    /**
     * 根据创建时间删除数据
     *
     * @param logCaptureTime 调用时间
     */
    @Modifying
    @Query(value = "delete FROM HvBmLogCapture l where  l.logCaptureTime < ?1")
    void deleteByLogCaptureTime(Date logCaptureTime);


}

package com.hvisions.log.capture.entity;

/**
 * <p>Title: HvBmDataAudit</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/11/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(indexes = {@Index(name = "idx_log_audit_entity", columnList = "entity"),
    @Index(name = "idx_log_audit_time", columnList = "updateTime"),
    @Index(name = "idx_log_audit_entity_history", columnList = "serviceName,entity,entityId")})
public class HvBmDataAudit {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    /**
     * 服务名
     */
    @NotBlank
    private String serviceName;

    /**
     * 实体主键
     */
    @NotBlank
    private String entityId;

    /**
     * 对象编码
     */
    @NotBlank
    private String entity;
    /**
     * 修改人
     */
    @NotNull
    private Integer useId;

    /**
     * 数据名称
     */
    @NotBlank
    private String entityName;

    /**
     * 修改内容
     */
    @Lob
    private String json;

    /**
     * 修改时间
     */
    @NotNull
    private Date updateTime;

    /**
     * 修改类型，1：新增，2：修改，3：删除
     */
    @NotNull
    private Integer type;

    /**
     * 简介
     */
    private String comment;
}










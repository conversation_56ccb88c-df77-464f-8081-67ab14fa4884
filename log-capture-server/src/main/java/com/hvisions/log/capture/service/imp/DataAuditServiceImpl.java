package com.hvisions.log.capture.service.imp;

import com.hvisions.auth.client.BaseUserClient;
import com.hvisions.auth.dto.user.UserInfoDto;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.common.dto.DataAuditDTO;
import com.hvisions.log.capture.common.dto.DataAudtiQuery;
import com.hvisions.log.capture.dao.DataAuditMapper;
import com.hvisions.log.capture.entity.HvBmDataAudit;
import com.hvisions.log.capture.repository.DataAuditRepository;
import com.hvisions.log.capture.service.DataAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>Title: DataAuditServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/11/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class DataAuditServiceImpl implements DataAuditService {
    @Autowired
    DataAuditRepository repository;
    @Autowired
    DataAuditMapper mapper;
    @Autowired
    BaseUserClient client;

    /**
     * 分页查询
     *
     * @return 分页数据
     */
    @Override
    public Page<DataAuditDTO> getPage(DataAudtiQuery queryDTO) {
        Page<DataAuditDTO> result = PageHelperUtil.getPage(mapper::findPage, queryDTO);
        //把用户名字塞进去
        try {
            ResultVO<List<UserInfoDto>> userInfoResult = client.getUserInfoByIds(result.getContent().stream()
                .map(DataAuditDTO::getUseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
            if (userInfoResult.isSuccess() && userInfoResult.getData() != null) {
                List<DataAuditDTO> content = result.getContent();
                for (DataAuditDTO entry : content) {
                    entry.setUserName(userInfoResult.getData().stream()
                        .filter(t -> t.getId().equals(entry.getUseId()))
                        .map(UserInfoDto::getUserName)
                        .findFirst()
                        .orElse(""));
                }
            }
        } catch (Exception ex) {
            log.warn("用户服务异常:{}", ex.getMessage());
        }
        return result;
    }

    /**
     * 获取历史数据
     *
     * @param serviceName 服务名
     * @param entity      实体
     * @param entityId    实体id
     * @return 历史数据
     */
    @Override
    public List<DataAuditDTO> getHistory(String serviceName, String entity, String entityId) {
        List<HvBmDataAudit> history = repository.findHistory(serviceName, entity, entityId);
        //把用户名字塞进去
        List<DataAuditDTO> result = DtoMapper.convertList(history, DataAuditDTO.class);
        try {
            ResultVO<List<UserInfoDto>> userInfoResult = client.getUserInfoByIds(result.stream()
                .map(DataAuditDTO::getUseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
            if (userInfoResult.isSuccess() && userInfoResult.getData() != null) {
                for (DataAuditDTO entry : result) {
                    entry.setUserName(userInfoResult.getData().stream()
                        .filter(t -> t.getId().equals(entry.getUseId()))
                        .map(UserInfoDto::getUserName)
                        .findFirst()
                        .orElse(""));
                }
            }
        } catch (Exception ex) {
            log.warn("用户服务异常:{}", ex.getMessage());
        }
        return result;
    }

    /**
     * 记录数据修改日志
     *
     * @param dto 审计信息
     */
    @Override
    public void createAudit(DataAuditDTO dto) {
        HvBmDataAudit entity = DtoMapper.convert(dto, HvBmDataAudit.class);
        repository.save(entity);
    }

    /**
     * 清理过期日志
     *
     * @param dataAuditDay 过期时间
     */
    @Override
    public void deleteByDay(int dataAuditDay) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_MONTH, -dataAuditDay);
        repository.deleteByAuditTime(cal.getTime());
    }
}










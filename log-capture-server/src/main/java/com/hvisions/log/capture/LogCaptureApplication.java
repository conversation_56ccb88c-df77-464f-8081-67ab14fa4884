package com.hvisions.log.capture;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <p>Title: SysBase</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.hvisions.log.capture.dao")
@EnableScheduling
@EnableFeignClients(basePackages = {"com.hvisions.auth.client"})
public class LogCaptureApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogCaptureApplication.class, args);
    }

}

package com.hvisions.log.capture.entity;

/**
 * <p>Title: FeedBack</p>
 * <p>Description: 反馈信息 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021-8-6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@Entity
public class HvBmLogFeedBack extends SysBase {
    /**
     * 提交人
     */
    @NotBlank(message = "提交人姓名不能为空")
    private String userName;
    /**
     * 产品名称版本号
     */
    private String productNameVersion;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 描述
     */
    private String description;
    /**
     * 问题分类
     */
    private String type;
    /**
     * 截图
     */
    private String picture;
    /**
     * 文档
     */
    private String document;
}










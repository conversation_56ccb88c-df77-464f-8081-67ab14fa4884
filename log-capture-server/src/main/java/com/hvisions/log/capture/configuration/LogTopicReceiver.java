package com.hvisions.log.capture.configuration;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.log.capture.common.dto.LogCaptureDTO;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.log.capture.service.LogCaptureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.listener.exception.ListenerExecutionFailedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <p>Title: TopicReceiver</p>
 * <p>Description: TopicReceiver</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class LogTopicReceiver {
    private final LogCaptureService logCaptureService;

    @Autowired
    public LogTopicReceiver(LogCaptureService logCaptureService) {
        this.logCaptureService = logCaptureService;
    }


    @Bean
    public RabbitListenerErrorHandler logErrorHandler() {
        return new RabbitListenerErrorHandler() {
            @Override
            public Object handleError(Message message, org.springframework.messaging.Message<?> message1, ListenerExecutionFailedException e) throws Exception {
                log.error("log insert error:" + message.toString(), e);
                return null;
            }
        };
    }

    /**
     * 监听队列
     *
     * @param dto 传入参数
     */
    @RabbitListener(queues = TopicRabbitConfig.LOG_QUEUE, errorHandler = "logErrorHandler")
    public void process(LogDto dto) {
        logCaptureService.logRecord(DtoMapper.convert(dto, LogCaptureDTO.class));
    }

}










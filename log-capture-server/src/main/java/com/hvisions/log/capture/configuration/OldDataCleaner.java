package com.hvisions.log.capture.configuration;

import com.hvisions.log.capture.service.DataAuditService;
import com.hvisions.log.capture.service.LogCaptureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <p>Title: ScheduledTasks</p >
 * <p>Description: 清理旧数据</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
@Slf4j
public class OldDataCleaner {
    @Value("${hvisions.logCapture.log-clear-day:30}")
    private int day;
    @Value("${hvisions.dataAudit.audit-clear-day:30}")
    private int dataAuditDay;

    @Autowired
    LogCaptureService logCaptureService;
    @Autowired
    DataAuditService dataAuditService;


    /**
     * 周期执行一次
     */
    @Scheduled(cron = "0 30 1 * * ?")
    public void reportCurrentTime() {
        log.info("开始删除过期接口日志，日志失效日数：{} 天", day);
        logCaptureService.deleteByLogCaptureTime(day);
        logCaptureService.deleteByLogMesToPi(day);
        logCaptureService.deleteByLogPiToMes(day);
        log.info("清除过期接口日志结束");


        log.info("开始删除过期审计日志，日志失效日数：{} 天", dataAuditDay);
        dataAuditService.deleteByDay(dataAuditDay);
        log.info("清除过期审计日志结束");
    }
}

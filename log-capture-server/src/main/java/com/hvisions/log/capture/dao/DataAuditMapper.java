package com.hvisions.log.capture.dao;

import com.hvisions.log.capture.common.dto.DataAuditDTO;
import com.hvisions.log.capture.common.dto.DataAudtiQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: logCaptureMapper</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/10/30</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Mapper
@Component
public interface DataAuditMapper {


    /**
     * 查询审计记录
     *
     * @param query 查询条件
     * @return 审计信息
     */
    List<DataAuditDTO> findPage(@Param("query") DataAudtiQuery query);
}
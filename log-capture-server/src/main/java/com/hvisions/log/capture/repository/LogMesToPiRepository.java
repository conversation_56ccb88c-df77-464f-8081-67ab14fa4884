package com.hvisions.log.capture.repository;

import com.hvisions.log.capture.entity.HvBmLogCapture;
import com.hvisions.log.capture.entity.HvBmLogMesToPi;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <p>Title: DemoEntityRepository</p>
 * <p>Description: 系统行政部门仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface LogMesToPiRepository extends JpaRepository<HvBmLogMesToPi, Integer> {

    @Modifying
    @Query(value = "delete FROM HvBmLogMesToPi l where  l.logCaptureTime < ?1")
    void deleteByLogMesToPi(Date time);
}

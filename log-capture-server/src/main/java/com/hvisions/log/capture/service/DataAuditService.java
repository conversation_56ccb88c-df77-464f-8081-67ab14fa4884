package com.hvisions.log.capture.service;

import com.hvisions.log.capture.common.dto.DataAuditDTO;
import com.hvisions.log.capture.common.dto.DataAudtiQuery;
import com.hvisions.log.capture.common.dto.LogQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: DataAuditService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/11/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface DataAuditService {
    /**
     * 分页查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    Page<DataAuditDTO> getPage(DataAudtiQuery queryDTO);

    /**
     * 查询实体历史记录
     *
     * @param serviceName 服务名
     * @param entityName  实体名
     * @param entityId    实体id
     * @return 实体修改历史
     */
    List<DataAuditDTO> getHistory(String serviceName, String entityName, String entityId);


    /**
     * 记录数据修改日志
     *
     * @param dto 审计信息
     */
    void createAudit(DataAuditDTO dto);

    /**
     * 清理过期日志
     * @param dataAuditDay 过期时间
     */
    void deleteByDay(int dataAuditDay);
}

    
    
    
    
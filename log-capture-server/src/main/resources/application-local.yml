#dev配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: **********************************************************************************************
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: admin
  application:
    name: log-leiming-test
eureka:
  client:
    service-url:
      defaultZone: http://************:8763/eureka/
  instance:
    prefer-ip-address: true
    instance-id: log-capture-dev:${server.port}


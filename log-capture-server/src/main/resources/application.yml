#默认公共配置
spring:
  mail:
    host: smtp.mxhichina.com
    username: <EMAIL>
    password: xxxx
    properties:
      from: <EMAIL>
  profiles:
    active: dev
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  cache:
    type: redis
  redis:
    host: ************
    port: 6379
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    #是否输入Jpa生成的sql语句,生产环境可以关闭
    show-sql: false
    #数据库生成策略，如果打开会根据entity对象生成数据库。尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名
  application:
    name: log-capture
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  cloud:
    refresh:
      #为了解决springboot与spring cloud数据库初始化检查添加的配置项的循环依赖问题所添加
      refreshable: none
mybatis:
  typeAliasesPackage: com.hvisions.log-capture.entitys
  mapperLocations: classpath:mapper/*.xml
h-visions:
  feedback:
    mail: <EMAIL>
  swagger:
    enable: true
  service-name: 日志服务
  logCapture:
    #定时清除日志的参数配置  day 天数
    log-clear-day: 30
  dataAudit:
    audit-clear-day: 30
#健康检查
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: ${h-visions.service-name}
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8763/eureka/
  instance:
    prefer-ip-address: true
    instance-id: log-capture:${server.port}
server:
  port: 9017
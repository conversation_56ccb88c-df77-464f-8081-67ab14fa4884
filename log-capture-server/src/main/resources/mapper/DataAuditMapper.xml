<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.log.capture.dao.DataAuditMapper">
    <select id="findPage" resultType="com.hvisions.log.capture.common.dto.DataAuditDTO">
        select * from hv_bm_data_audit t
        <where>
            <if test="query.serviceName != null and query.serviceName != ''">
                t.service_name like #{query.serviceName}
            </if>
            <if test="query.entity != null and query.entity != ''">
                and t.entity like #{query.entity}
            </if>
            <if test="query.entityId != null and query.entityId != ''">
                and t.entity_id like #{query.entityId}
            </if>
            <if test="query.useId != null ">
                and t.use_id = #{query.useId}
            </if>
            <if test="query.useId != null ">
                and t.use_id = #{query.useId}
            </if>
            <if test="query.type != null ">
                and t.use_id = #{query.type}
            </if>
            <if test="query.updateTimeStart != null and query.updateTimeEnd != null">
                and t.update_time between #{query.updateTimeStart} and #{query.updateTimeEnd}
            </if>
        </where>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.log.capture.dao.LogCaptureMapper">

    <resultMap id="log" type="com.hvisions.log.capture.common.dto.LogCaptureDTO">
    </resultMap>

    <select id="getLogByQuery" resultMap="log" parameterType="com.hvisions.log.capture.common.dto.LogQueryDTO"
            databaseId="sqlserver">
        select t1.id,t1.log_type,t1.log_parameter,t1.log_modular,t1.log_exception_message,
        t1.log_invocation,t1.location,t1.log_capture_time,t1.controller_name,t1.method_name,
        t2.user_name as logInvocationName
        from hv_bm_log_capture t1
        left join authority.dbo.sys_user t2
        on t1.log_invocation = t2.id
        <where>
            <if test="dto.logType !=null">
                t1.log_type = #{dto.logType}
            </if>
            <if test="dto.logInvocation !=null and dto.logInvocation != &apos;&apos;">
                and t2.user_name like concat('%', #{dto.logInvocation},'%')
            </if>
            <if test="dto.methodName !=null and dto.methodName != &apos;&apos; ">
                and t1.method_name like concat('%',#{dto.methodName},'%')
            </if>
            <if test="dto.controllerName !=null and dto.controllerName != &apos;&apos;">
                and t1.controller_name like concat('%',#{dto.controllerName},'%')
            </if>
            <if test="dto.logModular !=null and dto.logModular != &apos;&apos;">
                and t1.log_modular like concat('%',#{dto.logModular},'%')
            </if>
        </where>
    </select>
    <select id="getLogByQuery" resultMap="log" parameterType="com.hvisions.log.capture.common.dto.LogQueryDTO"
            databaseId="mysql">
        select t1.id,t1.log_type,t1.log_parameter,t1.log_modular,t1.log_exception_message,
        t1.log_invocation,t1.location,t1.log_capture_time,t1.controller_name,t1.method_name,
        t2.user_name as logInvocationName
        from hv_bm_log_capture t1
        left join authority.sys_user t2
        on t1.log_invocation = t2.id
        <where>
            <if test="dto.logType !=null">
                t1.log_type = #{dto.logType}
            </if>
            <if test="dto.logInvocation !=null and dto.logInvocation != ''">
                and t2.user_name like concat('%', #{dto.logInvocation},'%')
            </if>
            <if test="dto.methodName !=null and dto.methodName != ''">
                and t1.method_name like concat('%',#{dto.methodName},'%')
            </if>
            <if test="dto.controllerName !=null and dto.controllerName != ''">
                and t1.controller_name like concat('%',#{dto.controllerName},'%')
            </if>
            <if test="dto.logModular !=null and dto.logModular != ''">
                and t1.log_modular like concat('%',#{dto.logModular},'%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                and t1.log_capture_time between #{dto.startTime} and #{dto.endTime}
            </if>
        </where>
    </select>
    <select id="getLogMaxId" resultType="java.lang.Long">
        select max(t1.id)
        from hv_bm_log_capture t1
        left join authority.sys_user t2
        on t1.log_invocation = t2.id
        <where>
            <if test="dto.logType !=null">
                t1.log_type = #{dto.logType}
            </if>
            <if test="dto.logInvocation !=null and dto.logInvocation != ''">
                and t2.user_name like concat('%', #{dto.logInvocation},'%')
            </if>
            <if test="dto.methodName !=null and dto.methodName != ''">
                and t1.method_name like concat('%',#{dto.methodName},'%')
            </if>
            <if test="dto.controllerName !=null and dto.controllerName != ''">
                and t1.controller_name like concat('%',#{dto.controllerName},'%')
            </if>
            <if test="dto.logModular !=null and dto.logModular != ''">
                and t1.log_modular like concat('%',#{dto.logModular},'%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                and t1.log_capture_time between #{dto.startTime} and #{dto.endTime}
            </if>
        </where>
    </select>
    <select id="getLogMinId" resultType="java.lang.Long">
        select min(t1.id)
        from hv_bm_log_capture t1
        left join authority.sys_user t2
        on t1.log_invocation = t2.id
        <where>
            <if test="dto.logType !=null">
                t1.log_type = #{dto.logType}
            </if>
            <if test="dto.logInvocation !=null and dto.logInvocation != ''">
                and t2.user_name like concat('%', #{dto.logInvocation},'%')
            </if>
            <if test="dto.methodName !=null and dto.methodName != ''">
                and t1.method_name like concat('%',#{dto.methodName},'%')
            </if>
            <if test="dto.controllerName !=null and dto.controllerName != ''">
                and t1.controller_name like concat('%',#{dto.controllerName},'%')
            </if>
            <if test="dto.logModular !=null and dto.logModular != ''">
                and t1.log_modular like concat('%',#{dto.logModular},'%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                and t1.log_capture_time between #{dto.startTime} and #{dto.endTime}
            </if>
        </where>
    </select>
</mapper>

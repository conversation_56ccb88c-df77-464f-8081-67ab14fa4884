package com.hvisions.log.capture.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Target;
import java.util.Calendar;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LogCaptureServiceTest {
    @Value("${logCapture.log-clear-day}")
    private int day;

    @Autowired
    LogCaptureService logCaptureService;

    @Test
    @Transactional
    public void getDateX() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -day);
        System.out.println("七天前的时间是" + cal.getTime());
    }

    @Test
    @Transactional
    public void testLogRecord() {

    }

}

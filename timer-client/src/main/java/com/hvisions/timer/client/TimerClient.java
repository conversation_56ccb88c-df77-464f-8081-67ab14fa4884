package com.hvisions.timer.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.timer.dto.PredictionTimerDTO;
import com.hvisions.timer.dto.TimerDTO;
import com.hvisions.timer.dto.TimerQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: DemoClient</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "timer", path = "/timer", fallbackFactory = TimerClientFallBack.class)
public interface TimerClient {

    /**
     * 通过timerIdList获取timer
     *
     * @param timerIds timerId集合
     * @return timer集合
     */
    @PostMapping("/getByTimerIds")
    ResultVO<List<TimerDTO>> getByTimerIds(@RequestBody List<Integer> timerIds);

    /**
     * 根据id查询timer
     *
     * @param timerId timerId
     * @return timer对象
     */
    @GetMapping("/getByTimerId/{timerId}")
    ResultVO<TimerDTO> getByTimerId(@PathVariable int timerId);


    /**
     * 根据名称搜索timer列表
     *
     * @param timerQueryDTO 查询对象
     * @return 分页信息
     */
    @PostMapping("/getTimerByName")
    @ApiOperation(value = "根据Timer名称分页查询")
    ResultVO<HvPage<TimerDTO>> getTimerByName(@RequestBody TimerQueryDTO timerQueryDTO);


    /**
     * 创建一个周期对象
     *
     * @param timerDTO 周期对象
     * @return 对象id
     */
    @ApiOperation(value = "创建一个周期对象")
    @PostMapping("/createTimer")
    ResultVO<Integer> createTimer(@RequestBody TimerDTO timerDTO);


    /**
     * 创建一个周期对象,并立即启动
     *
     * @param timerDTO 周期对象
     * @return 对象id
     */
    @ApiOperation(value = "创建一个周期对象,并立即启动")
    @PostMapping("/createTimerAndStart")
    ResultVO<Integer> createTimerAndStart(@RequestBody TimerDTO timerDTO);

    /**
     * 更新一个周期实体
     *
     * @param timerDTO 周期实体
     * @return 对象id
     */
    @ApiOperation(value = "更新一个周期实体")
    @PutMapping("/updateTimer")
    ResultVO<Integer> updateTimer(@RequestBody TimerDTO timerDTO);

    /**
     * 根据id删除Timer
     *
     * @param timerId timer id
     * @return vo
     */
    @ApiOperation(value = "根据id删除Timer")
    @DeleteMapping("/deleteTimerById/{timerId}")
    ResultVO deleteTimerById(@PathVariable int timerId);

    /**
     * 根据id列表删除Timer
     *
     * @param timerIds timerId列表
     * @return vo
     */
    @ApiOperation(value = "根据id列表删除Timer")
    @DeleteMapping("/deleteTimerByIds")
    ResultVO deleteTimerByIds(@RequestBody List<Integer> timerIds);

    /**
     * 根据timerId启动定时器
     *
     * @param timerId timerId
     * @return vo
     */
    @ApiOperation(value = "根据timerId启动定时器")
    @PutMapping("/startTimer")
    ResultVO startTimer(int timerId);

    /**
     * 根据timerId停止定时器
     *
     * @param timerId timerId
     * @return vo
     */
    @PutMapping("/stopTimer")
    @ApiOperation(value = "根据timerId停止定时器")
    ResultVO stopTimer(int timerId);


    /**
     * 根据TimerId，获取cron表达式
     *
     * @param timerId timer主键
     * @return cron表达式
     */
    @GetMapping("/getCronStringByTimerId/{timerId}")
    @ApiOperation(value = "根据TimerId，获取cron表达式")
    ResultVO<String> getCronStringByTimerId(@PathVariable int timerId);

    /**
     * 根据TimerDTO，获取cron表达式
     *
     * @param timerDTO timerDTO
     * @return corn表达式
     */
    @PostMapping("/getCronStringByTimerDto")
    @ApiOperation(value = "根据TimerDTO，获取cron表达式")
    ResultVO<String> getCronStringByTimerDto(@RequestBody TimerDTO timerDTO);

    /**
     * 根据timerId获取周期执行记录信息
     *
     * @param timerId 条件
     * @return timer信息
     */
    @ApiOperation(value = "根据timerId获取执行记录信息")
    @PostMapping(value = "/getTimerInfoById/{timerId}")
    ResultVO<Map> getTimerInfoById(@PathVariable Integer timerId);

    /**
     * 根据timerId获取timer下次执行时间
     *
     * @param timerId 周期id
     * @return 下次执行时间
     */
    @ApiOperation(value = "根据timerId获取timer下次执行时间")
    @GetMapping(value = "/getNextExecutionTime/{timerId}")
    ResultVO<Date> getNextExecutionTime(@PathVariable int timerId);

    /**
     * 通过TimerID获取指定时间内的运行轨迹
     *
     * @param predictionTimerDTO 查询条件
     * @return list列表
     */
    @ApiOperation(value = "通过TimerID获取指定时间内的运行轨迹")
    @PostMapping(value = "/getPredictionTimerByTimerId")
    ResultVO<List<String>> getPredictionTimerByTimerId(@RequestBody PredictionTimerDTO predictionTimerDTO);


    /**
     * 通过TimerID获取指定时间内的运行轨迹
     *
     * @param predictionTimerDTO 查询条件
     * @return list列表
     */
    @ApiOperation(value = "通过TimerID获取指定时间内的运行轨迹")
    @PostMapping(value = "/getPredictionTimerByCron")
    ResultVO<List<String>> getPredictionTimerByCron(@RequestBody PredictionTimerDTO predictionTimerDTO);

}

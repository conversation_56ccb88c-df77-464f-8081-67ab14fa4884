package com.hvisions.timer.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.timer.dto.PredictionTimerDTO;
import com.hvisions.timer.dto.TimerDTO;
import com.hvisions.timer.dto.TimerQueryDTO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: TimerClientFallBack</p >
 * <p>Description: FallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class TimerClientFallBack extends BaseFallbackFactory<TimerClient> {

    @Override
    public TimerClient getFallBack(ResultVO vo) {
        return new TimerClient() {
            @Override
            public ResultVO<List<String>> getPredictionTimerByTimerId(PredictionTimerDTO predictionTimerDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<String>> getPredictionTimerByCron(PredictionTimerDTO predictionTimerDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<TimerDTO>> getByTimerIds(List<Integer> timerIds) {
                return vo;
            }

            @Override
            public ResultVO<TimerDTO> getByTimerId(int timerId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<TimerDTO>> getTimerByName(TimerQueryDTO timerQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createTimer(TimerDTO timerDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createTimerAndStart(TimerDTO timerDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateTimer(TimerDTO timerDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteTimerById(int timerId) {
                return vo;
            }

            @Override
            public ResultVO deleteTimerByIds(List<Integer> timerIds) {
                return vo;
            }

            @Override
            public ResultVO startTimer(int timerId) {
                return vo;
            }

            @Override
            public ResultVO stopTimer(int timerId) {
                return vo;
            }

            @Override
            public ResultVO<String> getCronStringByTimerId(int timerId) {
                return vo;
            }

            @Override
            public ResultVO<String> getCronStringByTimerDto(TimerDTO timerDTO) {
                return vo;
            }

            @Override
            public ResultVO<Map> getTimerInfoById(Integer timerId) {
                return vo;
            }

            @Override
            public ResultVO<Date> getNextExecutionTime(int timerId) {
                return vo;
            }
        };
    }
}
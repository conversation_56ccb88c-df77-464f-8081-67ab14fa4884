package com.hvisions.quality.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.common.excel.LocalDateConverter;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class MonthPlanMainPageExcel {

    @ExcelProperty(value = "酿酒中心")
    private String centerName;

    @ExcelProperty(value = "计划开始时间", converter = LocalDateConverter.class)
    private LocalDate planBeginDate;

    @ExcelProperty(value = "计划结束时间", converter = LocalDateConverter.class)
    private LocalDate planEndDate;

    @ExcelProperty(value = "计划投粮KG")
    private BigDecimal feeding;

    @ExcelProperty(value = "实际投粮KG")
    private BigDecimal actualFeeding;

    @ExcelProperty(value = "计划耗粮KG")
    private BigDecimal consumption;

    @ExcelProperty(value = "实际耗粮KG")
    private BigDecimal actualConsumption;

    @ExcelProperty(value = "计划产出KG")
    private BigDecimal winOutput;

    @ExcelProperty(value = "实际产出KG")
    private BigDecimal actualWinOutput;

    @ExcelProperty(value = "计划执行进度")
    private BigDecimal schedule;
}










/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.quality.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ParameterExcel {
    @ExcelProperty(value = "曲斗名称")
    private String name;

    @ExcelProperty(value = "曲斗条码")
    private String barcode;

    @ExcelProperty(value = "曲斗型号")
    private String type;

    @ExcelProperty(value = "曲斗规格")
    private String specification;
}
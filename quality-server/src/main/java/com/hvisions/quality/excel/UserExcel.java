package com.hvisions.quality.excel;

/**
 * <p>Title: UserExcel</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/6/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.common.excel.LocalDateConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@Getter
@Setter
@ToString
public class UserExcel {
    /**
     * 名称(实体保存时会校验此字段，异常统一处理此信息)
     */
    @ExcelProperty(value = "用户名")
    private String userName;
    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;
    /**
     * 日期
     */
    @ExcelProperty(value = "日期", converter = LocalDateConverter.class)
    private LocalDate date;
}










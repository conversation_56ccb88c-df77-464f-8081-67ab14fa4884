package com.hvisions.quality.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class AvsBaseDataDto {

    @JsonProperty("deliveryNumber")
    private String deliveryNumber;

    @JsonProperty("vehicleNo")
    private String vehicleNo;

    @JsonProperty("cargoName")
    private String cargoName;

    @JsonProperty("cargoSpec")
    private String cargoSpec;

    // 发货单位
    @JsonProperty("srcName")
    private String srcName;

    // 收货单位
    @JsonProperty("dstName")
    private String dstName;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("businessType")
    private Integer businessType;

    @JsonProperty("type")
    private Integer type;

}

package com.hvisions.quality.sap.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 10:15
 */
@Getter
@Setter
@NoArgsConstructor
public class SapBaseRequestDto {
    public SapBaseRequestDto(Request request) {
        this.request = request;
    }

    @JsonProperty("REQUEST")
    private Request request;

    @Data
    public static class Request {
        public Request(Head head, List list) {
            this.head = head;
            this.list = list;
        }

        @JsonProperty("HEAD")
        private Head head;

        @JsonProperty("LIST")
        private List list;


        @Data
        public static class Head {
            public Head() {
                this.account = "NJMES";
                this.password = "";
                this.consumer = "NJMES";
                this.use = "";
                this.comments = "NJMES";
                this.count = "1";
                final String transaction = "LZLJ_NJMES_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
                this.biTransactionId = transaction;
                this.srvLevel = "SAP";
                final String now = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
                this.requestDate = now;
                this.company = "1000";
            }

            @JsonProperty("ACCOUNT")
            private String account;

            @JsonProperty("PASSWORD")
            private String password;

            @JsonProperty("CONSUMER")
            private String consumer;

            @JsonProperty("USE")
            private String use;

            @JsonProperty("COMMENTS")
            private String comments;

            @JsonProperty("COUNT")
            private String count;

            @JsonProperty("BIZTRANSACTIONID")
            private String biTransactionId;

            @JsonProperty("SRVLEVEL")
            private String srvLevel;

            @JsonProperty("REQUESTDATE")
            private String requestDate;

            @JsonProperty("COMPANY")
            private String company;
        }


        @Data
        public static class List {

            public List(IvInput ivInput,String ivItfId) {
                this.ivInput = ivInput;
                this.ivItfId = ivItfId;
                this.ivOrderNumber = "";
            }

            @JsonProperty("IV_INPUT")
            private IvInput ivInput;

            @JsonProperty("IV_ITF_ID")
            private String ivItfId;

            @JsonProperty("IV_ORDER_NUMBER")
            private String ivOrderNumber;

            @Data
            public static class IvInput {
                public IvInput(Control control, java.util.List<Input> input) {
                    this.control = control;
                    this.input = input;
                }

                @JsonProperty("CONTROL")
                private Control control;


                @Data
                public static class Control {
                    public Control(String itfId) {
                        this.msgId = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
                        this.itfId = itfId;
                        this.sendDate = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
                        this.sendTime = DateUtil.format(new Date(), DatePattern.PURE_TIME_PATTERN);
                        this.extOne = "";
                        this.extTwo = "";
                        this.extThree = "";
                        this.extFour = "";
                        this.extFive = "";
                    }

                    @JsonProperty("MSGID")
                    private String msgId;

                    @JsonProperty("ITFID")
                    private String itfId;

                    @JsonProperty("SENDDATE")
                    private String sendDate;

                    @JsonProperty("SENDTIME")
                    private String sendTime;

                    @JsonProperty("EXT01")
                    private String extOne;

                    @JsonProperty("EXT02")
                    private String extTwo;

                    @JsonProperty("EXT03")
                    private String extThree;

                    @JsonProperty("EXT04")
                    private String extFour;

                    @JsonProperty("EXT05")
                    private String extFive;
                }

                @JsonProperty("INPUT")
                @JsonDeserialize(using = InputDeserializer.class)
                private java.util.List<Input> input;

                @Data
                public static class Input {
                    public Input(SapBaseHeaderDto header, java.util.List<SapBaseItemDto> item) {
                        this.header = header;
                        this.item = item;
                    }

                    @JsonProperty("HEADER")
                    private SapBaseHeaderDto header;

                    @JsonProperty("ITEM")
                    private java.util.List<SapBaseItemDto> item = new ArrayList<>();
                }

            }
        }
    }

    public static class InputDeserializer extends JsonDeserializer<java.util.List<Request.List.IvInput.Input>> {
        @Override
        public java.util.List<Request.List.IvInput.Input> deserialize(JsonParser jsonParser,
                                                                      DeserializationContext ctx) throws IOException, JsonProcessingException {
            java.util.List<Request.List.IvInput.Input> inputs = new ArrayList<>();
            if (jsonParser.getCurrentToken() == JsonToken.START_OBJECT) {
                final Request.List.IvInput.Input input = ctx.readValue(jsonParser, Request.List.IvInput.Input.class);
                inputs.add(input);
            } else if (jsonParser.getCurrentToken() == JsonToken.START_ARRAY) {
                java.util.List<Request.List.IvInput.Input> input = ctx.readValue(jsonParser, ctx.getTypeFactory().constructCollectionType(java.util.List.class, Request.List.IvInput.Input.class));
                inputs.addAll(input);
            }
            return inputs;
        }
    }
}

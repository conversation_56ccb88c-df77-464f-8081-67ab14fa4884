package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 * @title: SapTurnOverCancel
 * @projectName quality
 * @date 2022/7/5 17:55:20
 */
@Getter
@Setter
@NoArgsConstructor
public class SapTurnOverCancel extends SapBaseHeaderDto {

    /**
     * HEADER主键,多个主键的情况下用/串接
     */
    @JsonProperty("HEADER_KEY")
    private String headerKey;

    /**
     * 物料凭证
     */
    @JsonProperty("MBLNR")
    private String matDoc;

    /**
     * 物料凭证年度
     */
    @JsonProperty("MJAHR")
    private String docYear;

    /**
     * 凭证中的过帐日期
     */
    @JsonProperty("PSTNG_DATE")
    private String pstngDate;

}

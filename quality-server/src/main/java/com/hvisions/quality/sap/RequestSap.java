package com.hvisions.quality.sap;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.quality.sap.dto.*;
import com.hvisions.quality.utils.RestTemplateUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static com.hvisions.quality.sap.constant.SapConst.getBaseAuthHeaders;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 14:23
 */
@Component
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class RequestSap {
    @Autowired
    private LogCaptureClient logCaptureClient;

    public static <T extends SapBaseHeaderDto, E extends SapBaseItemDto> SapBaseRequestDto.Request.List.IvInput.Input getInput(T header, List<E> items) {
        SapBaseRequestDto.Request.List.IvInput.Input input = new SapBaseRequestDto.Request.List.IvInput.Input(header, (List<SapBaseItemDto>) items);
        return input;
    }

    public SapBaseResponseDto dockingSap(List<SapBaseRequestDto.Request.List.IvInput.Input> inputs, String itfId, String url) {
        SapBaseRequestDto.Request.List.IvInput.Control control = new SapBaseRequestDto.Request.List.IvInput.Control(itfId);
        SapBaseRequestDto.Request.List.IvInput ivInput = new SapBaseRequestDto.Request.List.IvInput(control, inputs);
        SapBaseRequestDto.Request.List list = new SapBaseRequestDto.Request.List(ivInput,itfId);
        SapBaseRequestDto.Request.Head head = new SapBaseRequestDto.Request.Head();
        SapBaseRequestDto.Request request = new SapBaseRequestDto.Request(head, list);
        SapBaseRequestDto sapBaseRequestDto = new SapBaseRequestDto(request);
        SapBaseResponseDto sapBaseResponseDto = baseMethod(sapBaseRequestDto, itfId, url);
        return sapBaseResponseDto;
    }


    //对接sap公共方法
    public SapBaseResponseDto baseMethod(SapBaseRequestDto sapBaseRequestDto, String itfId, String url) {
        final LogDto log = getBaseLog("同步sap", url,
                itfId);
        log.setLogType(2);

        //封装http请求返回结果
        final HttpInfo httpInfo = new HttpInfo();
        httpInfo.setRequestParams(sapBaseRequestDto);

        //点用sap接口
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = RestTemplateUtil.post(
                    url,
                    getBaseAuthHeaders(),
                    sapBaseRequestDto,
                    String.class);
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(httpInfo));
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "SAP接口调用失败，原因：" + e.getMessage());
        }

        final String responseBody = responseEntity.getBody();
        SapBaseResponseDto sapBaseResponseDto = JSONObject.parseObject(responseBody,SapBaseResponseDto.class);
        httpInfo.setResponseBody(sapBaseResponseDto);
        httpInfo.setStatusCode(responseEntity.getStatusCode().toString());
        log.setLogParameter(JSONObject.toJSONString(httpInfo));

        log.setLogType(1);
        logCaptureClient.logRecord(log);
        return sapBaseResponseDto;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用sap");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}

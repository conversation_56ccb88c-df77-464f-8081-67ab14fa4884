package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 * @title: sap订单工时同步DTO
 * @projectName quality
 * @date 2022/6/29 15:20:14
 */
@Getter
@Setter
@NoArgsConstructor
public class SapWorkHoursDTO extends SapBaseHeaderDto {
    private Integer workingHoursId;

    /**
     * 流水号
     */
    @JsonProperty("HEADER_KEY")
    private String headerKey;

    /**
     * 订单号
     */
    @JsonProperty("ORDERID")
    private String orderid;

    /**
     * 部分/最后确认
     */
    @JsonProperty("FIN_CONF")
    private String finConf = "1";

    /**
     * 过帐日期
     */
    @JsonProperty("POSTG_DATE")
    private String postgDate;

    /**
     * 确认计量单位
     */
    @JsonProperty("CONF_QUAN_UNIT")
    private String confQuanUnit = "kg";

    /**
     * 待确认的产量
     */
    @JsonProperty("YIELD")
    private String yield;

    /**
     * 老窖生产：工时
     */
    @JsonProperty("ISM01")
    private String ism01;

    /**
     * 老窖生产：机器
     */
    @JsonProperty("ISM02")
    private String ism02;

    /**
     * 老窖生产：动力
     */
    @JsonProperty("ISM03")
    private String ism03;

    /**
     * 老窖生产：其它
     */
    @JsonProperty("ISM04")
    private String ism04;

    /**
     * 老窖生产：辅助费用
     */
    @JsonProperty("ISM05")
    private String ism05;
}

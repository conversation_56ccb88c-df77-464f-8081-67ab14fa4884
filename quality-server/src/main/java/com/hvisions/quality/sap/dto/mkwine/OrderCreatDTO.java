package com.hvisions.quality.sap.dto.mkwine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: 
 * @title: 原酒生产订单创建DTO
 * @projectName quality
 * @date 2022/6/1 13:33:28
 */
@Data
@ApiModel(value = "原酒生产订单创建DTO")
public class OrderCreatDTO {
    @ApiModelProperty(value = "连窖订单id")
    private Integer id;

    @ApiModelProperty(value = "单窖订单id")
    private Integer sapOrderId;

    @ApiModelProperty(value = "窖池订单（单窖）")
    private String orderCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "订单类型id")
    private Integer orderTypeId;

    @ApiModelProperty(value = "创建时间")
    private Date creatTime;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "单窖号")
    private String pitCode;

    @ApiModelProperty(value = "糟赔排次")
    private Integer cycleNoId;

    @ApiModelProperty(value = "订单数量总计")
    private String quantity;
}

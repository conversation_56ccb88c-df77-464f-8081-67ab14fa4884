package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 
 * @title: SAP交酒任务DTO
 * @projectName quality
 * @date 2022/7/11 9:42:41
 */
@Getter
@Setter
@NoArgsConstructor
public class SapHandinDTO extends SapBaseHeaderDto {
    /**
     * 交酒任务id
     */
    private Integer handinId;

    /**
     * 序列号
     */
    @JsonProperty("HEADER_KEY")
    private String headerKey = "";

    /**
     * MES传工作中心
     */
    @JsonProperty("EXT01")
    private String ext01 = "";

    /**
     * 填1是创建，2是修改，3是删除
     */
    @JsonProperty("EXT02")
    private String ext02 = "";

    /**
     * 序列号
     */
    @JsonProperty("ID")
    private String id;

    /**
     * 交酒日期
     */
    @JsonProperty("JJRQ")
    private String jjrq = "";

    /**
     * sap订单号
     */
    @JsonProperty("AUFNR")
    private String aufnr = "";

    /**
     * 窖号
     */
    @JsonProperty("JHAO")
    private String jhao = "";

    /**
     * 糟醅类别
     */
    @JsonProperty("ZPLB")
    private String zplb = "";

    /**
     * 摘酒段数
     */
    @JsonProperty("ZJDC")
    private String zjdc = "";

    /**
     * 所属月份
     */
    @JsonProperty("YF")
    private String yf = "";

    /**
     * 坛号
     */
    @JsonProperty("TH")
    private String th = "";

    /**
     * 实际净重
     */
    @JsonProperty("JZ")
    private String jz;

    /**
     * 实际酒度
     */
    @JsonProperty("JD")
    private String jd;

    /**
     * 折合重量(KG,60° V/V)
     */
    @JsonProperty("ZHZL")
    private String zhzl;

    /**
     * 等级
     */
    @JsonProperty("DENGJ")
    private String dengj;

    /**
     * 己酸乙酯(g/100ml)
     */
    @JsonProperty("JSYZ")
    private String jsyz;

    /**
     * 乳酸乙酯(g/100ml)
     */
    @JsonProperty("RSYZ")
    private String rsyz;

    /**
     * 乙酸乙酯(g/100ml)
     */
    @JsonProperty("YSYZ")
    private String ysyz;

    /**
     * 丁酸乙酯(g/100ml)
     */
    @JsonProperty("DSYZ")
    private String dsyz;

    /**
     * 定级日期
     */
    @JsonProperty("DJRQ")
    private String djrq;

    /**
     * 删除标记
     */
    @JsonProperty("SCBJ")
    private String scbj;

    /**
     * 系统日期
     */
    @JsonProperty("XTRQ")
    private String xtrq;

    /**
     * 总酸
     */
    @JsonProperty("F")
    private String f;

    /**
     * 总酯
     */
    @JsonProperty("G")
    private String g;

    /**
     * 流水码
     */
    @JsonProperty("LSM")
    private String lsm;
}

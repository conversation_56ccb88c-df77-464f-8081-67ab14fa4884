package com.hvisions.quality.sap.dto.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 库存差异调账dto
 * @author: Jcao
 * @time: 2022/5/7 15:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "库存差异调账dto")
public class InventoryCheckDto {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "物料编号")
    private String material;

    @ApiModelProperty(value = "存储位置编码")
    private String stgeLoc;

    @ApiModelProperty(value = "数量")
    private String entryQnt;

}

package com.hvisions.quality.sap.dto.purchase;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: 采购订单冲销
 * @author: yyy
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderWriteOffHeaderDto extends SapBaseHeaderDto {

    /*
     * 凭证中的过帐日期
     */
    @JsonProperty("PSTNG_DATE")
    private String pstingDate;

    /*
     * 物料凭证
     */
    @JsonProperty("MBLNR")
    private String mblnr;

    /*
     * 物料凭证年度
     */
    @JsonProperty("MJAHR")
    private String mjahr;

}

package com.hvisions.quality.sap.dto.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: 库存查询
 * @author: Jcao
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class InventoryQueryHeaderDto extends SapBaseHeaderDto {

    /*
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 存储位置
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

}

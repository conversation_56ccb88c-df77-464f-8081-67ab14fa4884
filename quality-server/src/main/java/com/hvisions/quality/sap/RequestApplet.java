package com.hvisions.quality.sap;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.quality.sap.dto.HttpInfo;
import com.hvisions.quality.sap.dto.applet.AppletBaseDataDTO;
import com.hvisions.quality.sap.dto.applet.AppletBaseRequestDto;
import com.hvisions.quality.sap.dto.applet.AppletBaseResponseDto;
import com.hvisions.quality.utils.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static com.hvisions.quality.sap.constant.EsbConst.getBaseAuthHeaders;


@Slf4j
@Component
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class RequestApplet {
    @Autowired
    private LogCaptureClient logCaptureClient;

    public AppletBaseResponseDto dockingApplet(AppletBaseDataDTO data, String reqUrl, String methodName) {
        AppletBaseRequestDto.Request.Head head = new AppletBaseRequestDto.Request.Head();
        AppletBaseRequestDto.Request request = new AppletBaseRequestDto.Request(head, data);
        AppletBaseRequestDto appletBaseDataDto = new AppletBaseRequestDto(request);
        log.info("准备调用小程序接口,参数:" + JSONObject.toJSONString(appletBaseDataDto));
        AppletBaseResponseDto responseDto = baseMethod(appletBaseDataDto, reqUrl, methodName);
        log.info("小程序请求返回结果:" + JSONObject.toJSONString(responseDto));
        return responseDto;
    }


    public AppletBaseResponseDto baseMethod(AppletBaseRequestDto appletBaseDataDto, String url, String methodName) {
        final LogDto log = getBaseLog("小程序系统", url, methodName);
        log.setLogType(2);

        //封装http请求返回结果
        final HttpInfo httpInfo = new HttpInfo();
        httpInfo.setRequestParams(appletBaseDataDto);

        ResponseEntity<String> responseEntity;
        try {
            responseEntity = RestTemplateUtil.post(
                    url,
                    getBaseAuthHeaders(),
                    appletBaseDataDto,
                    String.class);
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(httpInfo));
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "小程序接口调用失败，原因：" + e.getMessage());
        }

        final String responseBody = responseEntity.getBody();
        AppletBaseResponseDto baseResponseDto = JSONObject.parseObject(responseBody, AppletBaseResponseDto.class);
        httpInfo.setResponseBody(baseResponseDto);
        httpInfo.setStatusCode(responseEntity.getStatusCode().toString());
        if(StringUtils.isNotEmpty(methodName)){
            log.setLogParameter(JSONObject.toJSONString(httpInfo));

            log.setLogType(1);
            logCaptureClient.logRecord(log);
        }
        return baseResponseDto;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用applet");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}

package com.hvisions.quality.sap.constant;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 9:57
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "sap")
public class SapConst implements InitializingBean {

    // cpxt esb url
    public static String CPXT_URI;
    // tms esb url
    public static String TMS_URI;
    // sap esb url
    public static  String SAP_URI;
    // 地磅 esb 正式 url
    public static String AVS_URI;
    // 甑口任务中控709 esb 正式 url
    public static String POT_URI_709;
    // 甑口任务中控713 esb 正式 url
    public static String POT_URI_713;
    // 甑口任务中控718 esb 正式 url
    public static String POT_URI_718;

    //打印
    public static String PRINT_LL;
    public static String PRINT_ZK;
    public static String PRINT_709;
    public static String PRINT_713;
    public static String PRINT_718;
    public static String PRINT_XM;
    public static String PRINT_SY;

    public static String PURCHASE_ORDER_CREATE_NO = "MESNJ012"; // MES创建采购订单同步SAP接口
    public static String PURCHASE_ORDER_UPDATE_NO = "MESNJ013"; // MES修改采购订单同步SAP接口
    public static  String PURCHASE_RECEIVING_NO = "MESNJ014"; // MES采购收货同步SAP接口，过账
    public static String PURCHASE_RECEIVING_REVOKE_NO = "MESNJ015"; // 物料凭证取消收货同步SAP接口,收货冲销

    public static String INVENTORY_ALLOCATION_NO = "MESNJ016"; // MES库存调拨同步SAP接口
    public static String INVENTORY_QUERY_NO = "MESNJ017"; // MES库存查询同步SAP接口
    public static String INVENTORY_MOVEMENT_NO = "MESNJ018"; // MES库存差异调账同步SAP接口
    public static String COST_LOSS_NO = "MESNJ019"; // MES成本中心耗损同步SAP接口
    public static String SYNC_STEAMER_NUM = "MESNJ020"; // 同步甑口数量

    public static String INVENTORY_WINe_ORDER_NO = "MESNJ002"; // 原酒生产订单创建接口(窖池）
    public static String INVENTORY_WINe_ORDER_INPUT_NO = "MESNJ004"; // MES原酒投料确认接口
    public static String INVENTORY_WINe_ORDER_WORK_HOURS_NO = "MESNJ006"; // MES原酒报工确认接口
    public static String INVENTORY_WINe_ORDER_OUTPUT_NO = "MESNJ008";// MES原酒收货接口
    public static String INVENTORY_WINe_ORDER_STATUS_NO = "MESNJ010";// MES订单状态修改接口
    public static String INVENTORY_WINe_ORDER_WORK_HOURS_CANCEL_NO = "MESNJ007";// MES原酒报工取消接口
    public static String INVENTORY_WINe_ORDER_HANDIN_TASK_NO = "MESNJ011"; // 班组交酒记录接口

    // 采供生成sap同步类型
    public static String SAP_RECEIVE = "收货同步";
    public static String SAP_LOST = "损耗同步";
    public static String SAP_WRITE_OFF = "sap冲销";
    public static String SAP_ISSUE = "发放同步";
    public static String SAP_STOCK = "库存调整同步";

    // 酿酒生产的sap同步类型
    public static String SAP_CREATE = "订单创建";
    public static String SAP_INPUT = "入窖投入同步";
    public static String SAP_OUTPUT_SEC = "出窖投入同步";
    public static String SAP_WORKHOURS = "工时同步";
    public static String SAP_TURNOVER = "翻窖修正同步";
    public static String SAP_OUTPUT = "订单产出";
    public static String SAP_WORKING = "在制工时同步";
    public static String SAP_RETURN = "修正同步";
    public static String SAP_CHANGE = "状态变更";
    public static String SAP_HANDIN = "交酒同步";
    public static String SAP_HANDIN_BATCH = "交酒批量同步";
    public static String SAP_HANDIN_UPDATE = "交酒理化同步";
    public static String SAP_STEAMER_NUM = "甑口数量同步";

    public static String SAP_INPUT_CANCEL = "入窖投入同步冲销";
    public static String SAP_OUTPUT_SEC_CANCEL = "出窖投入同步冲销";
    public static String SAP_WORKHOURS_CANCEL = "工时同步冲销";
    public static String SAP_TURNOVER_CANCEL = "翻窖修正同步冲销";
    public static String SAP_OUTPUT_CANCEL = "订单产出冲销";
    public static String SAP_RETURN_CANCEL = "修正同步冲销";
    public static String SAP_WORKING_CANCEL = "在制工时同步冲销";
    public static String SAP_HANDIN_CANCEL = "交酒同步冲销";

    public static HttpHeaders getBaseAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        return headers;
    }

    private String cpxtUri;
    private String tmsUri;
    private String sapUri;
    private String avsUri;
    private String potUri709;
    private String potUri713;
    private String potUri718;
    private String printLl;
    private String printZk;
    private String print709;
    private String print713;
    private String print718;
    private String printXM;
    private String printSY;

    @Override
    public void afterPropertiesSet() throws Exception {
        // cpxt esb 正式 url
        CPXT_URI = cpxtUri;
        // tms esb 正式 url
        TMS_URI = tmsUri;
        // sap esb 正式 url
        SAP_URI = sapUri;
        // 地磅 esb 正式 url
        AVS_URI = avsUri;
        // 甑口任务中控709 esb 正式 url
        POT_URI_709 = potUri709;
        // 甑口任务中控713 esb 正式 url
        POT_URI_713 = potUri713;
        // 甑口任务中控718 esb 正式 url
        POT_URI_718 = potUri718;
        //打印
        PRINT_LL = printLl;
        PRINT_ZK = printZk;
        PRINT_709 = print709;
        PRINT_713 = print713;
        PRINT_718 =  print718;
        PRINT_XM =  printXM;
        PRINT_SY =  printSY;
    }
}

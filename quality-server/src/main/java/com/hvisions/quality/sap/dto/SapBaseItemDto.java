package com.hvisions.quality.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 11:07
 */

@Getter
@Setter
@NoArgsConstructor
public class SapBaseItemDto {

    public SapBaseItemDto(String moveType) {
        this.moveType = moveType;
    }

    @JsonProperty("ITEM_KEY")
    private String itemKey;

    @JsonProperty("MOVE_TYPE")
    private String moveType;

    @JsonProperty("EXT01")
    private String extOne = "";

    @JsonProperty("EXT02")
    private String extTwo = "";

    @JsonProperty("EXT03")
    private String extThree = "";

    @JsonProperty("EXT04")
    private String extFour = "";

    @JsonProperty("EXT05")
    private String extFive = "";
}

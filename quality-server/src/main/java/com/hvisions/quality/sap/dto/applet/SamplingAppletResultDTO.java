package com.hvisions.quality.sap.dto.applet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 小程序检验结果推送DTO
 */
@Data
@ApiModel(description = "小程序检验结果推送DTO")
public class SamplingAppletResultDTO {

    @ApiModelProperty(value = "样品编码（年份-酒罐编码-流水码）")
    private String code;

    @ApiModelProperty(value = "已酸乙酯 (g/L)")
    private String jisuanYizhi;

    @ApiModelProperty(value = "乳酸乙酯(g/L)")
    private String rusuanYizhi;

    @ApiModelProperty(value = "乙酸乙酯(g/L)")
    private String yisuanYizhi;

    @ApiModelProperty(value = "丁酸乙酯(g/L)")
    private String dingsuanYizhi;

    @ApiModelProperty(value = "总酸(g/L)")
    private String zongSuan;

    @ApiModelProperty(value = "总酯(g/L)")
    private String zongZhi;

    @ApiModelProperty(value = "酒精度数")
    private String alcoholContent;

    @ApiModelProperty(value = "原度重量")
    private String originalWeight;

    @ApiModelProperty(value = "折合重量")
    private String equalsWeight;
}

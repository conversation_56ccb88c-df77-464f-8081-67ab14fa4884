package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseItemDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 * @title: sap订单产出同步 item DTO
 * @projectName quality
 * @date 2022/7/2 10:28:09
 */
@Getter
@Setter
@NoArgsConstructor
public class SapOutPutItemDTO extends SapBaseItemDto {
    /**
     * 行项目KEY
     */
    @JsonProperty("ITEM_KEY")
    private String itemKey;

    /**
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /**
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /**
     * 存储位置
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

    /**
     * 移动类型(库存管理)
     */
    @JsonProperty("MOVE_TYPE")
    private String moveType;

    /**
     * 以录入项单位表示的数量
     */
    @JsonProperty("ENTRY_QNT")
    private String entryQnt;

    /**
     * 条目单位
     */
    @JsonProperty("ENTRY_UOM")
    private String entryUom;
}

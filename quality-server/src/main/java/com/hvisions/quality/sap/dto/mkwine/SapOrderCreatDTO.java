package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 
 * @title: 原酒生产订单创建SAP DTO
 * @projectName quality
 * @date 2022/6/1 13:07:28
 */
@Getter
@Setter
@NoArgsConstructor
public class SapOrderCreatDTO extends SapBaseHeaderDto {
    /*
     * MES订单号
     */
    @JsonProperty("HEADER_KEY")
    private String headerKey;

    /*
     * MES 系统单据号
     */
    @JsonProperty("MES_ID")
    private String mesId;

    /*
     * 订单的物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 订单类型
     */
    @JsonProperty("ORDER_TYPE")
    private String orderType;

    /*
     * 订单数量总计
     */
    @JsonProperty("QUANTITY")
    private String quantity;

    /**
     * 全部订单项通用单位
     */
    @JsonProperty("QUANTITY_UOM")
    private String quantityUom;

    /**
     * 基本开始日期
     */
    @JsonProperty("BASIC_START_DATE")
    private String basicStartDate;

    /**
     * 基本完成日期
     */
    @JsonProperty("BASIC_END_DATE")
    private String basicEndDate;

    /**
     * 工作中心
     */
    @JsonProperty("ARBPL")
    private String arbpl;

    /**
     * 顺序号订单（窖号）
     */
    @JsonProperty("CY_SEQNR")
    private String cySeqnr;

    /**
     * 收货方
     */
    @JsonProperty("WEMPF")
    private String wempf;

    /**
     * 是否下达，X表示下达，否则不下达
     */
    @JsonProperty("EXT01")
    private String ext01 = "X";

}

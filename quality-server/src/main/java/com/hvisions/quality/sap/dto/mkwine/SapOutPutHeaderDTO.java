package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 * @title: sap订单产出同步 header DTO
 * @projectName quality
 * @date 2022/7/2 10:24:02
 */
@Getter
@Setter
@NoArgsConstructor
public class SapOutPutHeaderDTO extends SapBaseHeaderDto {
    /**
     * 唯一流水号
     *
     */
    @JsonProperty("HEADER_KEY")
    private String headerKey;

    /**
     * 生产订单号
     */
    @JsonProperty("DOCNO")
    private String docno;

    /**
     * 凭证中的过帐日期
     */
    @JsonProperty("PSTNG_DATE")
    private String pstngDate;

    /**
     * 凭证中的凭证日期
     */
    @JsonProperty("DOC_DATE")
    private String docDate;
}

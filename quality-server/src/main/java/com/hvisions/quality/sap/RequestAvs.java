package com.hvisions.quality.sap;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.quality.sap.constant.SapConst;
import com.hvisions.quality.sap.dto.*;
import com.hvisions.quality.utils.RestTemplateUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static com.hvisions.quality.sap.constant.SapConst.getBaseAuthHeaders;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:23
 */
@Component
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class RequestAvs {
    @Autowired
    private LogCaptureClient logCaptureClient;

    public static <T extends SapBaseHeaderDto, E extends SapBaseItemDto> SapBaseRequestDto.Request.List.IvInput.Input getInput(T header, List<E> items) {
        SapBaseRequestDto.Request.List.IvInput.Input input = new SapBaseRequestDto.Request.List.IvInput.Input(header, (List<SapBaseItemDto>) items);
        return input;
    }

    public AvsBaseResponseDto dockingAvs(AvsBaseDataDto data, String url) {
        AvsBaseRequestDto.Request.List list = new AvsBaseRequestDto.Request.List(data);
        AvsBaseRequestDto.Request.Head head = new AvsBaseRequestDto.Request.Head();
        AvsBaseRequestDto.Request request = new AvsBaseRequestDto.Request(head, list);
        AvsBaseRequestDto avsBaseRequestDto = new AvsBaseRequestDto(request);
        AvsBaseResponseDto avsBaseResponseDto = baseMethod(avsBaseRequestDto, SapConst.AVS_URI);
        return avsBaseResponseDto;
    }


    //对接avs公共方法
    public AvsBaseResponseDto baseMethod(AvsBaseRequestDto avsBaseRequestDto, String url) {
        final LogDto log = getBaseLog("地磅系统", url,
                "下发地磅");
        log.setLogType(2);

        //封装http请求返回结果
        final HttpInfo httpInfo = new HttpInfo();
        httpInfo.setRequestParams(avsBaseRequestDto);

        //点用sap接口
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = RestTemplateUtil.post(
                    url,
                    getBaseAuthHeaders(),
                    avsBaseRequestDto,
                    String.class);
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(httpInfo));
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "AVS接口调用失败，原因：" + e.getMessage());
        }
        System.out.println(responseEntity.getBody());

        final String responseBody = responseEntity.getBody();
        AvsBaseResponseDto vasBaseResponseDto = JSONObject.parseObject(responseBody, AvsBaseResponseDto.class);
        httpInfo.setResponseBody(vasBaseResponseDto);
        httpInfo.setStatusCode(responseEntity.getStatusCode().toString());
        log.setLogParameter(JSONObject.toJSONString(httpInfo));

        log.setLogType(1);
        logCaptureClient.logRecord(log);
        return vasBaseResponseDto;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用avs");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}

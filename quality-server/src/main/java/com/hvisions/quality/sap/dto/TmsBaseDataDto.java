package com.hvisions.quality.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class TmsBaseDataDto {

    @JsonProperty("QF1")
    private String qf1;

    @JsonProperty("QF2")
    private String qf2;

    @JsonProperty("QF3")
    private String qf3;

    // 空斗数
    @JsonProperty("kds")
    private String kds;

    // 变更类型： I新增,U修改
    @JsonProperty("BZLX")
    private String bglx;

    // 跨
    @JsonProperty("BZBH")
    private String bzbh;

}

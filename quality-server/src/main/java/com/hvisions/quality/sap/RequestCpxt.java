package com.hvisions.quality.sap;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.quality.sap.constant.SapConst;
import com.hvisions.quality.sap.dto.CpxtBaseDataDto;
import com.hvisions.quality.sap.dto.CpxtBaseRequestDto;
import com.hvisions.quality.sap.dto.CpxtBaseResponseDto;
import com.hvisions.quality.sap.dto.HttpInfo;
import com.hvisions.quality.utils.RestTemplateUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static com.hvisions.quality.sap.constant.SapConst.getBaseAuthHeaders;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 09:23
 */
@Component
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class RequestCpxt {
    @Autowired
    private LogCaptureClient logCaptureClient;

    public CpxtBaseResponseDto dockingCpxt(List<CpxtBaseDataDto> list, String url) {

        CpxtBaseRequestDto.Request.Head head = new CpxtBaseRequestDto.Request.Head();
        CpxtBaseRequestDto.Request request = new CpxtBaseRequestDto.Request(head, list);
        CpxtBaseRequestDto cpxtBaseRequestDto = new CpxtBaseRequestDto(request);
        CpxtBaseResponseDto cpxtBaseResponseDto = baseMethod(cpxtBaseRequestDto, SapConst.CPXT_URI);
        return cpxtBaseResponseDto;
    }


    //对接avs公共方法
    public CpxtBaseResponseDto baseMethod(CpxtBaseRequestDto cpxtBaseRequestDto, String url) {
        final LogDto log = getBaseLog("尝评系统", url,
                "推送尝评任务");
        log.setLogType(2);

        //封装http请求返回结果
        final HttpInfo httpInfo = new HttpInfo();
        httpInfo.setRequestParams(cpxtBaseRequestDto);

        //调用tms接口
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = RestTemplateUtil.post(
                    url,
                    getBaseAuthHeaders(),
                    cpxtBaseRequestDto,
                    String.class);
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(httpInfo));
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "cpxt接口调用失败，原因：" + e.getMessage());
        }
        System.out.println(responseEntity.getBody());

        final String responseBody = responseEntity.getBody();
        CpxtBaseResponseDto cpxtBaseResponseDto = JSONObject.parseObject(responseBody, CpxtBaseResponseDto.class);
        httpInfo.setResponseBody(cpxtBaseResponseDto);
        httpInfo.setStatusCode(responseEntity.getStatusCode().toString());
        log.setLogParameter(JSONObject.toJSONString(httpInfo));

        log.setLogType(1);
        logCaptureClient.logRecord(log);
        return cpxtBaseResponseDto;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用cpxt");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}

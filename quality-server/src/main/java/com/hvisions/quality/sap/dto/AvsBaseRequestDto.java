package com.hvisions.quality.sap.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:15
 */
@Getter
@Setter
@NoArgsConstructor
public class AvsBaseRequestDto {
    public AvsBaseRequestDto(Request request) {
        this.request = request;
    }

    @JsonProperty("REQUEST")
    private Request request;

    @Data
    public static class Request {
        public Request(Head head, List list) {
            this.head = head;
            this.list = list;
        }

        @JsonProperty("HEAD")
        private Head head;

        @JsonProperty("LIST")
        private List list;


        @Data
        public static class Head {
            public Head() {
                this.account = "QCH";
                this.password = "";
                this.consumer = "QCH";
                this.count = "1";
                final String transaction = "LZLJ_NJMES_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
                this.biTransactionId = transaction;
                this.sevLevel = "NJMES";
                final String now = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
                this.requestDate = now;
            }

            @JsonProperty("ACCOUNT")
            private String account;

            @JsonProperty("PASSWORD")
            private String password;

            @JsonProperty("CONSUMER")
            private String consumer;

            @JsonProperty("COUNT")
            private String count;

            @JsonProperty("BIZTRANSACTIONID")
            private String biTransactionId;

            @JsonProperty("SRVLEVEL")
            private String sevLevel;

            @JsonProperty("REQUESTDATE")
            private String requestDate;
        }


        @Data
        public static class List {

            public List(AvsBaseDataDto ivData) {
                this.ivData = ivData;
            }

            @JsonProperty("data")
            private AvsBaseDataDto ivData;

        }
    }
}

package com.hvisions.quality.sap.dto.applet;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class AppletBaseRequestDto {

    public AppletBaseRequestDto(Request request) {
        this.request = request;
    }

    @JsonProperty("REQUEST")
    private Request request;

    @Data
    public static class Request {
        public Request(Head head, AppletBaseDataDTO list) {
            this.head = head;
            this.list = list;
        }

        @JsonProperty("HEAD")
        private Head head;

        @JsonProperty("LIST")
        private AppletBaseDataDTO list;


        @Data
        public static class Head {
            public Head() {
                this.account = "LJMES";
                this.password = "";
                this.consumer = "LJMES";
                this.count = "1";
                final String transaction = "LZLJ_LJMES_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
                this.biTransactionId = transaction;
                this.sevLevel = "LJXJ";
                final String now = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
                this.requestDate = now;
            }

            @JsonProperty("ACCOUNT")
            private String account;

            @JsonProperty("PASSWORD")
            private String password;

            @JsonProperty("CONSUMER")
            private String consumer;

            @JsonProperty("COUNT")
            private String count;

            @JsonProperty("BIZTRANSACTIONID")
            private String biTransactionId;

            @JsonProperty("SRVLEVEL")
            private String sevLevel;

            @JsonProperty("REQUESTDATE")
            private String requestDate;
        }
    }
}

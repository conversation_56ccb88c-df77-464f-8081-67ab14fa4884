package com.hvisions.quality.sap.dto.purchase;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseItemDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 采购订单过账
 * @author: yyy
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderPostingItemDto extends SapBaseItemDto {

    /*
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 存储位置
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

    /*
     * 批次
     */
    @JsonProperty("BATCH")
    private String batch;

    /*
     * 移动类型 101
     */
    @JsonProperty("MOVE_TYPE")
    private String moveType;

    /*
     * 送货数量
     */
    @JsonProperty("ENTRY_QNT")
    private BigDecimal entryQut;

    /*
     * 送货单位
     */
    @JsonProperty("ENTRY_UOM")
    private String entryUom;

    /*
     * 采购订单号
     */
    @JsonProperty("PO_NUMBER")
    private String poNumber;

    /*
     * 订单行项目号
     */
    @JsonProperty("PO_ITEM")
    private String poItem;

    /*
     * 送货单号
     */
    @JsonProperty("XBLNR")
    private String xblnr;

}

package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 * @title: 生产订单状态变更DTO
 * @projectName quality
 * @date 2022/7/2 14:53:20
 */
@Getter
@Setter
@NoArgsConstructor
public class SapChangeStatusDTO extends SapBaseHeaderDto {
    /**
     * 生产订单状态变更流水号
     */
    @JsonProperty("HEADER_KEY")
    private String headerKey;

    /**
     * 订单号
     */
    @JsonProperty("AUFNR")
    private String aufnr;

    /**
     * 操作类型
     */
    @JsonProperty("OP_TYPE")
    private String opType;
}

package com.hvisions.quality.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: 物料查询数据
 * @author: Jca<PERSON>
 * @time: 2022/5/10 10:19
 */
@Getter
@Setter
@NoArgsConstructor
public class SapBaseOutputDto {

    /**
     * 过账凭证号
     */
    @JsonProperty("MBLNR")
    private String mblnr;

    /**
     * 过账年度
     */
    @JsonProperty("MJAHR")
    private String mjahr;

    @JsonProperty("HEADER_KEY")
    private String headerKey;

    @JsonProperty("MSGTY")
    private String msgty;

    @JsonProperty("MSGTX")
    private String msgtx;

    @JsonProperty("EBELN")
    private String ebeln;

    /*
     * 库存查询数据：工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 库存查询数据：仓库位置
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

    /*
     * 库存查询数据：物料号码
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 库存查询数据：数量
     */
    @JsonProperty("QTY")
    private String qty;

    /*
     * 库存查询数据：单位
     */
    @JsonProperty("UNIT")
    private String unit;

    @JsonProperty("EXT01")
    private String extOne = "";

    @JsonProperty("EXT02")
    private String extTwo = "";

    @JsonProperty("EXT03")
    private String extThree = "";

    @JsonProperty("EXT04")
    private String extFour = "";

    @JsonProperty("EXT05")
    private String extFive = "";

    @JsonProperty("AUFNR")
    private String aufnr;

    @JsonProperty("CONF_NO")
    private String confNo;

    @JsonProperty("CONF_CNT")
    private String confCnt;

    @JsonProperty("MAT_DOC")
    private String matDoc;

    @JsonProperty("DOC_YEAR")
    private String docYear;

    @JsonProperty("ID")
    private String id;
}

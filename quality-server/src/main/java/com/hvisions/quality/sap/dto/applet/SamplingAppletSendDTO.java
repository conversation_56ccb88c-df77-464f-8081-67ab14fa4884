package com.hvisions.quality.sap.dto.applet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "小程序批量推送数据")
public class SamplingAppletSendDTO {

    @ApiModelProperty(value = "样品编码列表 -- 同步预处理数据使用")
    private List<String> samplingCodeList;

    @ApiModelProperty(value = "检验任务id列表 -- 同步理化数据使用")
    private List<Integer> taskIdList;
}

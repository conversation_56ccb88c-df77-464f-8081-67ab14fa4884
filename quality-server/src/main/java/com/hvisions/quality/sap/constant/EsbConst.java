package com.hvisions.quality.sap.constant;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "esb")
public class EsbConst implements InitializingBean {

    // 同步交酒预填数据 发送样品编码信息
    public static String APPLET_SAMPLING_SEND_URI;
    // 同步理化数据 发送基酒检验结果
    public static String APPLET_SAMPLING_RESULT_SEND_URI;
    public static HttpHeaders getBaseAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        return headers;
    }

    private String appletSamplingSendUri;

    private String appletSamplingResultSendUri;

    @Override
    public void afterPropertiesSet() {
        // 同步交酒预填数据 发送样品编码信息
        APPLET_SAMPLING_SEND_URI = appletSamplingSendUri;
        // 同步理化数据 发送基酒检验结果
        APPLET_SAMPLING_RESULT_SEND_URI = appletSamplingResultSendUri;
    }
}

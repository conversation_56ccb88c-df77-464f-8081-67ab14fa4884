package com.hvisions.quality.sap.dto.applet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "同步交酒预填数据发送小程序DTO")
public class AppletBaseDataDTO {

    //样品编码，接口交互唯一
    @ApiModelProperty(value = "样品编码（年份-酒罐编码-流水码）")
    private String code;

    //同步交酒预填数据 请求数据
    @ApiModelProperty(value = "酒罐编码")
    private String jarCode;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "发酵时间（天）")
    private Integer fermentingTime;

    @ApiModelProperty(value = "窖龄")
    private Integer pitAge;

    @ApiModelProperty(value = "糟源类别")
    private String zaoyuanCategory;

    @ApiModelProperty(value = "交酒提报人(工号)")
    private String createBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "定级方式:集中定级(Concentrated)or现场定级(OnSite)")
    private String ratedType;

    @ApiModelProperty(value = "采样批次 固定传1")
    private String batchNum = "1";

    @ApiModelProperty(value = "采样时间")
    private String samplingTime;

    @ApiModelProperty(value = "采样人员(工号)")
    private String samplingBy;

    @ApiModelProperty(value = "计划年份(样品编码前面部分)")
    private String planYear;

    @ApiModelProperty(value = "原度重量")
    private String originalWeight;

    //理化分析数据 请求数据
    @ApiModelProperty(value = "已酸乙酯 (g/L)")
    private String jisuanYizhi;

    @ApiModelProperty(value = "乳酸乙酯(g/L)")
    private String rusuanYizhi;

    @ApiModelProperty(value = "乙酸乙酯(g/L)")
    private String yisuanYizhi;

    @ApiModelProperty(value = "丁酸乙酯(g/L)")
    private String dingsuanYizhi;

    @ApiModelProperty(value = "总酸(g/L)")
    private String zongSuan;

    @ApiModelProperty(value = "总酯(g/L)")
    private String zongZhi;

    @ApiModelProperty(value = "酒精度数")
    private String alcoholContent;

    @ApiModelProperty(value = "折合重量")
    private String equalsWeight;
}

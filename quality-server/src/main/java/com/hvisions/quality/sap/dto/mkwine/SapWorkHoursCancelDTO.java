package com.hvisions.quality.sap.dto.mkwine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 * @title: sap订单工时同步冲销DTO
 * @projectName quality
 * @date 2022/7/5 16:30:10
 */
@Getter
@Setter
@NoArgsConstructor
public class SapWorkHoursCancelDTO extends SapBaseHeaderDto {

    @JsonProperty("HEADER_KEY")
    private String headerKey;

    @JsonProperty("ORDERID")
    private String orderid;

    @JsonProperty("POSTG_DATE")
    private String postgDate;

    @JsonProperty("CONF_NO")
    private String confNo;

    @JsonProperty("CONF_CNT")
    private String confCnt;
}

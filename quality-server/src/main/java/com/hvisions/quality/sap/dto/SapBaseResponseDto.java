package com.hvisions.quality.sap.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/25 11:36
 */
@Getter
@Setter
@NoArgsConstructor
public class SapBaseResponseDto {
    @JsonProperty("ES_MESSAGE")
    private EsMessage esMessage;

    @Data
    public static class EsMessage {
        @JsonProperty("MSGTY")
        private String msgty;

        @JsonProperty("MSGTX")
        private String msgtx;
    }

    @JsonProperty("EV_OUTPUT")
    private EvOutput evOutput;

    @Data
    public static class EvOutput {
        @JsonProperty("ES_MSG")
        private SapBaseMessageDto esMsg;

        @JsonProperty("OUTPUT")
        private List<SapBaseOutputDto> output;

        @JsonProperty("ET_LIST")
        private String etList = "";
    }
}

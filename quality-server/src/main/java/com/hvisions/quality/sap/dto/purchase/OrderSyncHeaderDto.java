package com.hvisions.quality.sap.dto.purchase;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: 采购订单信息同步
 * @author: Jcao
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderSyncHeaderDto extends SapBaseHeaderDto {

    /*
     * 凭证类型
     */
    @JsonProperty("DOC_TYPE")
    private String docType;

    /*
     * 供应商帐户号
     */
    @JsonProperty("VENDOR")
    private String vendor;

    /*
     * 公司代码
     */
    @JsonProperty("COMP_CODE")
    private String compCode;

    /*
     * 创建日期
     */
    @JsonProperty("CREAT_DATE")
    private String creatDate;

    /*
     * 创建者
     */
    @JsonProperty("CREATED_BY")
    private String createdBy;

    /*
     * 采购组织
     */
    @JsonProperty("PURCH_ORG")
    private String purchOrg;

    /*
     * 采购组
     */
    @JsonProperty("PUR_GROUP")
    private String purGroup;

    /*
     * 外部参考
     */
    @JsonProperty("OUR_REF")
    private String ourRef;
}

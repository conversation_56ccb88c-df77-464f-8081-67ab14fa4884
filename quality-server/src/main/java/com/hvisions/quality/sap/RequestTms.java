package com.hvisions.quality.sap;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.quality.sap.constant.SapConst;
import com.hvisions.quality.sap.dto.HttpInfo;
import com.hvisions.quality.sap.dto.TmsBaseDataDto;
import com.hvisions.quality.sap.dto.TmsBaseRequestDto;
import com.hvisions.quality.sap.dto.TmsBaseResponseDto;
import com.hvisions.quality.utils.RestTemplateUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static com.hvisions.quality.sap.constant.SapConst.getBaseAuthHeaders;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/06/15 18:23
 */
@Component
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class RequestTms {
    @Autowired
    private LogCaptureClient logCaptureClient;

    public TmsBaseResponseDto dockingTms(TmsBaseDataDto data, String url) {
        TmsBaseRequestDto.Request.List list = DtoMapper.convert(data, TmsBaseRequestDto.Request.List.class);

        TmsBaseRequestDto.Request.Head head = new TmsBaseRequestDto.Request.Head();
        head.setBglx(data.getBglx());
        head.setBzbh(data.getBzbh());
        TmsBaseRequestDto.Request request = new TmsBaseRequestDto.Request(head, list);
        TmsBaseRequestDto tmsBaseRequestDto = new TmsBaseRequestDto(request);
        TmsBaseResponseDto tmsBaseResponseDto = baseMethod(tmsBaseRequestDto, SapConst.TMS_URI);
        return tmsBaseResponseDto;
    }


    //对接avs公共方法
    public TmsBaseResponseDto baseMethod(TmsBaseRequestDto tmsBaseRequestDto, String url) {
        final LogDto log = getBaseLog("运输系统", url,
                "同步tms");
        log.setLogType(2);

        //封装http请求返回结果
        final HttpInfo httpInfo = new HttpInfo();
        httpInfo.setRequestParams(tmsBaseRequestDto);

        //调用tms接口
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = RestTemplateUtil.post(
                    url,
                    getBaseAuthHeaders(),
                    tmsBaseRequestDto,
                    String.class);
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(httpInfo));
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "tms接口调用失败，原因：" + e.getMessage());
        }
        System.out.println(responseEntity.getBody());

        final String responseBody = responseEntity.getBody();
        TmsBaseResponseDto tmsBaseResponseDto = JSONObject.parseObject(responseBody, TmsBaseResponseDto.class);
        httpInfo.setResponseBody(tmsBaseResponseDto);
        httpInfo.setStatusCode(responseEntity.getStatusCode().toString());
        log.setLogParameter(JSONObject.toJSONString(httpInfo));

        log.setLogType(1);
        logCaptureClient.logRecord(log);
        return tmsBaseResponseDto;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用tms");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}

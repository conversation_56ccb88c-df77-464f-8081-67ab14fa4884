package com.hvisions.quality.sap.dto.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Description: 库存调拨dto
 * @author: Jcao
 * @time: 2022/5/7 10:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "库存调拨dto")
public class InventoryAllocationDto {

    @ApiModelProperty(value = "任务号")
    private String orderNo;

    @ApiModelProperty(value = "物料编号")
    private String material;

    @ApiModelProperty(value = "源仓库编码")
    private String slogrt;

    @ApiModelProperty(value = "目标仓库编码")
    private String dlogrt;

    @ApiModelProperty(value = "送货数量")
    private BigDecimal entryQnt;

    @ApiModelProperty("过账日期")
    private LocalDate certificateDate;

}

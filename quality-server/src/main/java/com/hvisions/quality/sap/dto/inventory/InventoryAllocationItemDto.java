package com.hvisions.quality.sap.dto.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseItemDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 库存调拨
 * @author: Jcao
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class InventoryAllocationItemDto extends SapBaseItemDto {

    /*
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 移动类型(库存管理)
     */
    @JsonProperty("MOVE_TYPE")
    private String moveType;

    /*
     * 移动类型(库存管理)
     */
    @JsonProperty("SLOGRT")
    private String slogrt;

    /*
     * 目标仓库
     */
    @JsonProperty("DLOGRT")
    private String dlogrt;

    /*
     * 送货数量
     */
    @JsonProperty("ENTRY_QNT")
    private BigDecimal entryQnt;

    /*
     * 送货单位
     */
    @JsonProperty("ENTRY_UOM")
    private String entryUom;

}

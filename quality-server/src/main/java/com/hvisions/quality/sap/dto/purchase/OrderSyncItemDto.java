package com.hvisions.quality.sap.dto.purchase;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseItemDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 采购订单信息同步
 * @author: Jca<PERSON>
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderSyncItemDto extends SapBaseItemDto {

    @JsonProperty("PO_ITEM")
    private String poItem;

    /*
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 存储位置
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

    /*
     * 数量
     */
    @JsonProperty("QUANTITY")
    private String quantity;

    /*
     * 采购订单计量单位
     */
    @JsonProperty("PO_UNIT")
    private String poUnit;

    /*
     * 采购凭证中的项目类别
     */
    @JsonProperty("ITEM_CAT")
    private String itemCat;

    /*
     * 需求者/要求者名称
     */
    @JsonProperty("PREQ_NAME")
    private String preqName;

    /*
     * 交货日期
     */
    @JsonProperty("DDATE")
    private String ddate;

    /*
     * 价格
     */
    @JsonProperty("PRICE")
    private BigDecimal price;

    /*
     * 基准值（每）
     */
    @JsonProperty("PER")
    private BigDecimal per;

    /*
     * 价格计量单位
     */
    @JsonProperty("BBPRM")
    private String bbprm;
}

package com.hvisions.quality.sap.dto.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseItemDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: 库存差异调账
 * @author: Jcao
 * @time: 2022/5/7 15:15
 */
@Getter
@Setter
@NoArgsConstructor
public class InventoryCheckItemDto extends SapBaseItemDto {

    /*
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 存储位置
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

    /*
     * 移动类型(库存管理)
     */
    @JsonProperty("MOVE_TYPE")
    private String moveType;

    /*
     * 数量
     */
    @JsonProperty("ENTRY_QNT")
    private String entryQnt;

    /*
     * 单位
     */
    @JsonProperty("ENTRY_UOM")
    private String entryUom;
}

package com.hvisions.quality.sap.dto.purchase;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseHeaderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description: 采购订单过账
 * @author: yyy
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderPostingHeaderDto extends SapBaseHeaderDto {

    /*
     * 凭证中的过帐日期
     */
    @JsonProperty("PSTNG_DATE")
    private String pstingDate;

    /*
     * 凭证中的凭证日期
     */
    @JsonProperty("DOC_DATE")
    private String docDate;

    /*
     * 凭证抬头文本
     */
    @JsonProperty("HEADER_TXT")
    private String headerTxt;

}

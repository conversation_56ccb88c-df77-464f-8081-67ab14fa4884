package com.hvisions.quality.sap.dto.cost.loss;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hvisions.quality.sap.dto.SapBaseItemDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 成本损耗item
 * @author: yyy
 * @time: 2022/5/7 10:14
 */
@Getter
@Setter
@NoArgsConstructor
public class CostLossItemDto extends SapBaseItemDto {


    /*
     * 物料编号
     */
    @JsonProperty("MATERIAL")
    private String material;

    /*
     * 工厂
     */
    @JsonProperty("PLANT")
    private String plant;

    /*
     * 仓库
     */
    @JsonProperty("STGE_LOC")
    private String stgeLoc;

    /*
     * 批次
     */
    @JsonProperty("BATCH")
    private String batch;

    /*
     * 移动类型(库存管理)，目前默认为Z51/Z52，公司1100与工厂1100损耗差异
     */
    @JsonProperty("MOVE_TYPE")
    private String moveType;

    /*
     * 自动按MES数据作为损耗数据处理
     */
    @JsonProperty("ENTRY_QNT")
    private BigDecimal entryQut;

    /*
     * 送货单位
     */
    @JsonProperty("ENTRY_UOM")
    private String entryUom;

    /**
     * 成本中心
     */
    @JsonProperty("KOSTL")
    private String kostl;

    /**
     * 移动原因,默认0011
     */
    @JsonProperty("GRUND")
    private String grund;

    /**
     * 总账科目,传递值默认660908005
     */
    @JsonProperty("SAKTO")
    private String sakto;

    /**
     * 传递值默认11001010 黄舣园区
     */
    @JsonProperty("PRCTR")
    private String prctr;

}

package com.hvisions.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hvisions.quality.dto.VehicleTransportDTO;
import com.hvisions.quality.dto.VehicleTransportQueryDTO;
import com.hvisions.quality.entity.VehicleTransport;
import org.apache.ibatis.annotations.Param;

public interface VehicleTransportMapper extends BaseMapper<VehicleTransport> {
    /**
     * 分页查询车辆运输管理配置信息
     * @param page
     * @param queryDTO
     * @return
     */
    IPage<VehicleTransportDTO> getVehicleTransportsByPage(IPage<VehicleTransportDTO> page,@Param("query") VehicleTransportQueryDTO queryDTO);

    /**
     * 根據id查詢
     * @param Id
     * @return
     */
    VehicleTransport getVehicleTransportsById(@Param("id") Integer Id);
}

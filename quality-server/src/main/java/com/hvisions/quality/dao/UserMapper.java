package com.hvisions.quality.dao;

import com.hvisions.quality.dto.QueryDTO;
import com.hvisions.quality.dto.UserDTO;
import com.hvisions.quality.entity.HvUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: DemoEntityMapper</p>
 * <p>Description: 使用Mybatis进行操作 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/6/6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Mapper
@Component
public interface UserMapper {

    /**
     * 获取实体信息
     *
     * @param queryDTO 实体查询条件
     * @return 实体信息
     */
    List<HvUser> getUser(QueryDTO queryDTO);

    /**
     * 根据角色编码获取随机角色名
     * @param roleCode
     * @return
     */
    String getUserNameByRoleCode(@Param("roleCode") String roleCode);

    List<UserDTO> getUserNameByRoleCodeIn(@Param("codes") List<String> roleCode);

    /**
     *  根据id获取用户电话
     * @param id
     * @return
     */
    String getUserPoneById(@Param("id") Integer id);

}










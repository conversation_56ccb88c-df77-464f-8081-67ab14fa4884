package com.hvisions.quality.task;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.quality.quality.dao.InspectionTaskMapper;
import com.hvisions.quality.quality.entity.TQaInspectionTask;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.note.SendSms;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@EnableScheduling
@Component
@Slf4j
public class InspectionTaskExecuteTask {

    @Resource
    private InspectionTaskMapper inspectionTaskMapper;

    @Resource
    private UserClient userClient;


    /**
     *  每天下午3点定时检查，如果有未完成的检验任务，则推送短信通知，无则不推送
     **/
    @Scheduled(cron = "0 0 15  * * ? ")
    @SchedulerLock(name = "completeFermentTask")
    public void completeFermentTask() {
        Date startTime = DateUtil.datePlus(-1, DateUtil.getDayLastSecond(new Date()));
        log.info("开始检查是否还有未完成的检验任务");
        List<TQaInspectionTask> tQaInspectionTasks = inspectionTaskMapper.selectList(new LambdaUpdateWrapper<TQaInspectionTask>().ge(TQaInspectionTask::getReceiveData, startTime).eq(TQaInspectionTask::getDeleted, false).eq(TQaInspectionTask::getInspectionType, "0").in(TQaInspectionTask::getState, "6", "7"));
        if (!CollectionUtils.isEmpty(tQaInspectionTasks)) {
            log.info("共获取到{}条未完成的检验任务", tQaInspectionTasks.size());
            List<String> stringList = tQaInspectionTasks.stream().map(TQaInspectionTask::getSamplingCode).collect(Collectors.toList());
            //拼接样品编码
            String samplingCodes = String.join(",", stringList);
            String[] msg = new String[]{
                    String.valueOf(tQaInspectionTasks.size()),
                    samplingCodes
            };
            // 粮检组长 角色：GrainLeader id: 9
            this.sendPrimerReportNotice(9, "2338290", msg);
        } else {
            log.info("未获取到未完成的检验任务");
        }
    }

    /**
     * @Description 未完成的检验任务发送短信通知 GrainLeader
     **/
    public void sendPrimerReportNotice(Integer roleId, String template, String[] msg) {
        try {
            List<UserDTO> userDTOS = userClient.getUsersByRole(roleId).getData();
            List<String> phones = new ArrayList<>();
            for (UserDTO userDTO : userDTOS) {
                if (StringUtils.isNotEmpty(userDTO.getMobilePhone())) {
                    phones.add("+86" + userDTO.getMobilePhone());
                }
            }
            SendSms.sendNote(template, msg, phones.toArray(new String[phones.size()]));

        } catch (Exception e) {
            log.error("发送短信失败~~~~~~~~");
            e.printStackTrace();
        }
    }
}

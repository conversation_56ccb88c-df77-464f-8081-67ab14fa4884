package com.hvisions.quality.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Collection;

public class CopyUtil {
    private static final Logger log = LoggerFactory.getLogger(CopyUtil.class);
    public static final String[] ignoreProperties = new String[]{"id", "delFlag", "createBy", "createTime", "updateBy", "updateTime", "tenantId"};
    public static final String[] mongoIgnoreProperties = new String[]{"id", "delFlag", "createBy", "createTime"};

    public CopyUtil() {
    }

    public static <T> T simpleCopy(Object source, Class<T> targetClass) {
        T target = null;

        try {
            target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
        } catch (InstantiationException | IllegalAccessException | BeansException var4) {
            log.error("error:" + var4.getMessage(), var4);
        }

        return target;
    }

    public static <T> T simpleCopy(Object source, Class<T> targetClass, String... ignoreProperties) {
        T target = null;

        try {
            target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target, ignoreProperties);
        } catch (InstantiationException | IllegalAccessException | BeansException var5) {
            log.error("error:" + var5.getMessage(), var5);
        }

        return target;
    }

    public static void copyPropertiesExcludeNull(Object source, Object target) {
        try {
            Assert.notNull(source, "Source must not be null");
        } catch (IllegalArgumentException var14) {
            log.error("Source object is null!", var14);
        }

        try {
            Assert.notNull(target, "Target must not be null");
        } catch (IllegalArgumentException var13) {
            log.error("Target object is null!", var13);
        }

        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);
        PropertyDescriptor[] var4 = targetPds;
        int var5 = targetPds.length;

        for(int var6 = 0; var6 < var5; ++var6) {
            PropertyDescriptor targetPd = var4[var6];
            Method writeMethod = targetPd.getWriteMethod();
            if (writeMethod != null) {
                PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null) {
                    Method readMethod = sourcePd.getReadMethod();
                    if (readMethod != null && ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType())) {
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }

                            Object value = readMethod.invoke(source);
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }

                            if (value != null) {
                                writeMethod.invoke(target, value);
                            }
                        } catch (Throwable var12) {
                            log.error("Could not copy property '" + targetPd.getName() + "' from source to target", var12);
                        }
                    }
                }
            }
        }

    }

    public static void copyProperties(Object source, Object target) {
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception var3) {
            log.error("error:" + var3.getMessage(), var3);
        }

    }

    public static void copyProperties(Object source, Object target, String... ignoreProperties) {
        try {
            BeanUtils.copyProperties(source, target, ignoreProperties);
        } catch (BeansException var4) {
            log.error("error:" + var4.getMessage(), var4);
        }

    }

    public static void copyProperties(Object source, Object target, Collection<String> ignoreProperties) {
        try {
            if (ignoreProperties != null && ignoreProperties.size() > 0) {
                String[] arr = (String[])ignoreProperties.toArray(new String[ignoreProperties.size()]);
                BeanUtils.copyProperties(source, target, arr);
            } else {
                BeanUtils.copyProperties(source, target);
            }
        } catch (BeansException var4) {
            log.error("error:" + var4.getMessage(), var4);
        }

    }
}

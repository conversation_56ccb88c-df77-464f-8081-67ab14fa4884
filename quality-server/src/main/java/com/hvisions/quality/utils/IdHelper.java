package com.hvisions.quality.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @BelongsProject: quality
 * @BelongsPackage: com.hvisions.quality.utils
 * <AUTHOR>
 * @Description: 获取流水号工具
 * @Date 2022-05-10  14:12
 * @Version: 1.0
 */
@Component
@Slf4j
public class IdHelper {

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取流水号
     * 默认加日期
     * 设置默认过期时间：1天
     * 默认3位流水号
     *
     * @param prefix
     * @return
     */
    public String getExpireId(String prefix) {
        return getId(prefix, true, 1, TimeUnit.DAYS, 3);
    }

    /**
     * 获取流水号
     * 默认加日期
     * 设置默认过期时间：1天
     * 自定义流水号
     *
     * @param prefix
     * @return
     */
    public String getExpireId(String prefix, Integer num) {
        return getId(prefix, true, 1, TimeUnit.DAYS, num);
    }

    /**
     * 获取流水号
     * 默认加日期
     * 不设置过期时间
     * 默认3位流水号
     *
     * @param prefix
     * @return
     */
    public String getId(String prefix) {
        return getId(prefix, false, 1, TimeUnit.DAYS, 3);
    }

    /**
     * 获取流水号
     * 默认加日期
     * 不设置过期时间
     * 自定义流水号
     *
     * @param prefix
     * @return
     */
    public String getId(String prefix, Integer num) {
        return getId(prefix, false, 1, TimeUnit.DAYS, num);
    }

    /**
     * 获取流水号
     * 默认加日期
     *
     * @param prefix
     * @param setExpire
     * @param timeOut
     * @param unit
     * @return
     */
    private String getId(String prefix, boolean setExpire, long timeOut, TimeUnit unit, Integer num) {

        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateString = LocalDate.now().format(formatters);
        String cacheKey = prefix + dateString;

        return getKeyValue(cacheKey, setExpire, timeOut, unit, num);
    }


    public String getKeyValue(String key, boolean setExpire, long timeOut, TimeUnit unit, int num) {
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(key, "1");
        String str = "%" + "0" + num + "d";
        if (num == 0){
            str = "%d";
        }
        if (Objects.nonNull(absent) && absent) {
            if (setExpire) {
                redisTemplate.expire(key, timeOut, unit);
            }
            return key + String.format(str, 1);
        }

        Long afterIncrease = redisTemplate.opsForValue().increment(key, 1);
        return key + String.format(str, afterIncrease);
    }


    /**
     * 年月日+中心车间+加上三位自增号
     *
     * @param key
     * @return
     */
    public String generate(String key) {
        long incr = incr(key, 1, 24 * 60 * 60);
        String code = SequenceUtils.getSequence(incr);
        log.info(key + code);
        return key + code;
    }

    /**
     * @param key   redis的 key值
     * @param delta 自增的增量
     * @return
     * @Title: incr
     * @Description: 获取redis自增序号
     */
    public Long incr(String key, long delta, long time) {
        try {
            Long l = redisTemplate.opsForValue().increment(key, delta);
            if (time > 0) {
                expire(key, time);
            }
            return l;
        } catch (Exception e) {
            log.error("redis获取" + key + "失败", e);
            return null;
        }
    }

    /**
     * @param key
     * @param time
     * @Title: expire
     * @Description: 设置过期时间
     */
    public void expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("redis设置" + key + "过期时间失败", e);
        }
    }

    /**
     * 简易锁
     * 用于多台服务器同时部署时需要 @PostConstruct 初始化的程序
     * @param key
     * @return
     */
    public Boolean tryGetLock(String key){

        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, "lock");
        if (Boolean.TRUE.equals(lock)){
            redisTemplate.expire(key, 30, TimeUnit.MINUTES);
        }

        return lock;
    }


}

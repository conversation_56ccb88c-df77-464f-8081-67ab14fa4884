package com.hvisions.quality.utils.cellmerge;

/**
 * @author: Deng<PERSON><PERSON>Tao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.quality.utils.merge.MergeCellInfoInit
 * @Date: 2022年07月25日 17:20
 * @Description: 初始化单元格合并信息
 */
public class MergeCellInfoInit {

    /**
     * 生成合并列单元格信息
     *
     * @param sheetName        sheet页名称
     * @param rowIndex         行号
     * @param startColumnIndex 开始列号
     * @param endColumnIndex   结束列号
     * @return
     */
    public static MergeCellModel createMergeColumnCellModel(String sheetName, int rowIndex, int startColumnIndex
            , int endColumnIndex) {
        return createMergeCellModel(sheetName, rowIndex, rowIndex, startColumnIndex, endColumnIndex);
    }

    /**
     * 生成合并单元格信息
     *
     * @param sheetName     sheet页名称
     * @param startRowIndex 开始行号
     * @param endRowIndex   结束行号
     * @param columnIndex   列号
     * @return
     */
    public static MergeCellModel createMergeRowCellModel(String sheetName, int startRowIndex, int endRowIndex, int columnIndex) {
        return createMergeCellModel(sheetName, startRowIndex, endRowIndex, columnIndex, columnIndex);
    }

    /**
     * 生成合并单元格信息
     *
     * @param sheetName        sheet页名称
     * @param startRowIndex    开始行号
     * @param endRowIndex      结束行号
     * @param startColumnIndex 开始列号
     * @param endColumnIndex   结束列号
     * @return
     */
    public static MergeCellModel createMergeCellModel(String sheetName, int startRowIndex, int endRowIndex, int startColumnIndex
            , int endColumnIndex) {
        MergeCellModel mergeCellModel = new MergeCellModel();
        // sheet页名称
        mergeCellModel.setSheetName(sheetName);
        // 开始行号
        mergeCellModel.setStartRowIndex(startRowIndex);
        // 结束行号
        mergeCellModel.setEndRowIndex(endRowIndex);
        // 开始列号
        mergeCellModel.setStartColumnIndex(startColumnIndex);
        // 结束列号
        mergeCellModel.setEndColumnIndex(endColumnIndex);
        return mergeCellModel;
    }
}

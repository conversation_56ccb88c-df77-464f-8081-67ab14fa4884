package com.hvisions.quality.utils.note;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/9 15:43
 */
@Data
public class SmsVo {
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("SendStatusSet")
    private Set<SendStatusSet> sendStatusSet;

    @Data
    public static class SendStatusSet {

        @JsonProperty("SerialNo")
        private String serialNo;

        @JsonProperty("PhoneNumber")
        private String phoneNumber;

        @JsonProperty("Fee")
        private String fee;

        @JsonProperty("SessionContext")
        private String sessionContext;

        @JsonProperty("Code")
        private String code;

        @JsonProperty("Message")
        private String message;

        @JsonProperty("IsoCode")
        private String isoCode;
    }
}

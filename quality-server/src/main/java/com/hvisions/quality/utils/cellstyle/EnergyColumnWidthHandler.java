package com.hvisions.quality.utils.cellstyle;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.List;

/**
 * @author: DengWeiTao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.quality.utils.cellstyle.CustomCellWriteWeightConfig
 * @Date: 2022年08月02日 15:15
 * @Description: 能源消耗明细excel格式
 */
public class EnergyColumnWidthHandler extends AbstractColumnWidthStyleStrategy {

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cell<PERSON><PERSON><PERSON>ist, Cell cell, Head head, Integer integer, Boolean isHead) {
        Sheet sheet = writeSheetHolder.getSheet();
        switch (cell.getColumnIndex()) {
            case 2 : sheet.setColumnWidth(cell.getColumnIndex(), 20 * 256); break;
            case 3 : sheet.setColumnWidth(cell.getColumnIndex(), 50 * 256); break;
            case 5 :
            case 7 :
            case 6 :
                sheet.setColumnWidth(cell.getColumnIndex(), 15 * 256); break;
        }
    }
}

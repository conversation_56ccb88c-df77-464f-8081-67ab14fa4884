package com.hvisions.quality.utils;


import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 定时线程
 * @date 2022/3/24 13:35
 */
@Component
public class IntervalTaskUtil {

    List<TaskInfo> taskInfoList = new ArrayList<>();

    ThreadPoolExecutor tpExecutor = new ThreadPoolExecutor(4, 8, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(Integer.MAX_VALUE), new ThreadPoolExecutor.AbortPolicy());

    public void postDelay(Runnable run, long delay) {
        TaskInfo taskInfo = new TaskInfo();
        taskInfo.setCreatTime(System.currentTimeMillis());
        taskInfo.setDelay(delay);
        taskInfo.setWork(run);
        taskInfoList.add(taskInfo);
    }


    @Scheduled(fixedRate = 1000)
    public void intervalTaskEngine() {
        if (!taskInfoList.isEmpty()) {
            ArrayList<TaskInfo> remove = new ArrayList<>();
            ArrayList<TaskInfo> taskInfos = new ArrayList<>(taskInfoList);
            for (TaskInfo taskInfo : taskInfos) {
                if (System.currentTimeMillis() - taskInfo.getCreatTime() > taskInfo.getDelay()) {
                    tpExecutor.execute(taskInfo.getWork());
                    remove.add(taskInfo);
                }
            }
            taskInfoList.removeAll(remove);
        }
    }

    static class TaskInfo {

        long creatTime;

        // 执行时间
        long delay;

        Runnable work;

        public long getDelay() {
            return delay;
        }

        public void setDelay(long delay) {
            this.delay = delay;
        }

        public Runnable getWork() {
            return work;
        }

        public void setWork(Runnable work) {
            this.work = work;
        }

        public long getCreatTime() {
            return creatTime;
        }

        public void setCreatTime(long creatTime) {
            this.creatTime = creatTime;
        }
    }
}
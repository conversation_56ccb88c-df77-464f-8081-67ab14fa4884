package com.hvisions.quality.utils;

import java.util.UUID;

public class StringUtil {
    public StringUtil() {
    }

    public static String replaceOther(String str) {
        return str != null && !"".equals(str) ? str.replaceAll("[\\pP+~$`^=|<>～｀＄＾＋＝｜＜＞￥×]", "").replaceAll(" ", "") : null;
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static boolean isEmpty(String str) {
        return str == null || "".equals(str) || "null".equals(str) || "undefined".equals(str) || str.trim().length() <= 0;
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isEmpty(Object str) {
        if (str == null) {
            return true;
        } else {
            return !isNotEmpty(str);
        }
    }

    public static boolean isNotEmpty(Object str) {
        if (str == null) {
            return false;
        } else {
            return !isEmpty(str.toString());
        }
    }

    public static String toString(Object str) {
        return isNotEmpty(str) ? str.toString() : null;
    }

    public static String getFirstChinese(String str) {
        try {
            if(isNotEmpty(str)){
                for (int i = 0;i<str.length();i++){
                    //将字符串拆开成单个的字符
                    String w= str.substring(i, i+1);
                    if(w.compareTo("\u4e00")>0&&w.compareTo("\u9fa5")<0){// \u4e00-\u9fa5 中文汉字的范围
                        return w;
                    }
                }
            }
        }catch (Exception e){

        }
        return "A";
    }

    /**
     * 数字转字母
     * 1：A
     * 2：B
     * @param num
     * @return
     */
    public static String numberToLetter(int num) {
        if (num <= 0) {
            return null;
        }
        String letter = "";
        num--;
        do {
            if (letter.length() > 0) {
                num--;
            }
            letter = ((char) (num % 26 + (int) 'A')) + letter;
            num = (int) ((num - num % 26) / 26);
        } while (num > 0);

        return letter;
    }

    public static String encode(String a) {
        return new StringBuffer(new sun.misc.BASE64Encoder().encode(a.getBytes())).reverse().toString();
    }

}
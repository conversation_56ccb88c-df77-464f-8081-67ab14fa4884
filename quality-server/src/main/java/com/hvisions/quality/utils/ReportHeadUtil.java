package com.hvisions.quality.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import java.lang.reflect.Field;
import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报表表头工具类
 *
 * @BelongsProject: quality
 * @BelongsPackage: com.hvisions.quality.utils
 * <AUTHOR>
 * @Date 2022-08-11  14:25
 * @Version: 1.0
 */
public class ReportHeadUtil {

    /**
     * 修改头部
     *
     * @param clazz
     * @param modifyMap
     * @return
     */
    public static Class modifyHead(Class clazz, Map<String, List<String>> modifyMap) throws NoSuchFieldException, IllegalAccessException {

        if (modifyMap == null || modifyMap.size() == 0) {
            return clazz;
        }

        for (String key : modifyMap.keySet()) {

            Field value = clazz.getDeclaredField(key);

            ExcelProperty apiParam = value.getAnnotation(ExcelProperty.class);
            if (apiParam == null) {
                continue;
            }

            value.setAccessible(true);
            java.lang.reflect.InvocationHandler invocationHandler = Proxy.getInvocationHandler(apiParam);
            Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
            memberValues.setAccessible(true);
            Map<String, Object> values = (Map<String, Object>) memberValues.get(invocationHandler);
            values.put("value", modifyMap.get(key).toArray(new String[0]));
        }

        return clazz;
    }

    /**
     * 修改指定顺序头部
     *
     * @param clazz
     * @param hashMap
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public static Class modifyHead(Class clazz, HashMap<Integer, String> hashMap) throws NoSuchFieldException, IllegalAccessException {

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ExcelProperty apiParam = field.getAnnotation(ExcelProperty.class);
            if (apiParam == null) {
                continue;
            }
            field.setAccessible(true);
            java.lang.reflect.InvocationHandler invocationHandler = Proxy.getInvocationHandler(apiParam);
            Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
            memberValues.setAccessible(true);
            String[] value1 = apiParam.value();

            for (Map.Entry<Integer, String> entry : hashMap.entrySet()) {
                if (entry.getKey() >= value1.length) {
                    continue;
                }
                value1[entry.getKey()] = entry.getValue();
            }

            Map<String, Object> values = (Map<String, Object>) memberValues.get(invocationHandler);
            values.put("value", value1);
        }

        return clazz;
    }


    /**
     * 修改指定顺序头部
     *
     * @param clazz
     * @param index
     * @param title
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public static Class modifyHead(Class clazz, Integer index, String title) throws NoSuchFieldException, IllegalAccessException {
        HashMap<Integer, String> hashMap = new HashMap<>();
        hashMap.put(index, title);
        return modifyHead(clazz, hashMap);
    }



}

package com.hvisions.quality.utils;

import java.math.BigDecimal;
import java.text.NumberFormat;

/**
 * <AUTHOR>
 * @description:
 * @title: NumberUtil
 * @projectName quality
 * @date 2022/9/14 11:12:44
 */
public class NumberUtil {

    /**
     * @Des 保留小数后几位 返回float
     * <AUTHOR>
     * @Date 2022/9/14 11:15:02
     * @Param
     * @Return
     */
    public static float floatDecimal(float num, int decimal) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(decimal);
        nf.setGroupingUsed(false);
        String format = nf.format(num);
        return Float.parseFloat(format);
    }

    /**
     * 保留num位小数 返回double
     * <AUTHOR>
     * @param value 值
     * @param num 小数位数
     */
    public static double toDouble(double value, int num){
        BigDecimal bigDecimal = new BigDecimal(Double.toString(value));
        return bigDecimal.setScale(num, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 保留num位小数 返回float
     * 这里的floatValue()可能会存在问题，推荐使用toDouble()
     * <AUTHOR>
     * @param value 值
     * @param num 小数位数
     */
    public static float toFloat(float value, int num){
        BigDecimal bigDecimal = new BigDecimal(Float.toString(value));
        return bigDecimal.setScale(num, BigDecimal.ROUND_HALF_UP).floatValue();
    }

    /**
     * 两个Integer类型值相加
     * @param i1 加数1
     * @param i2 加数2
     * @return 相加的和
     */
    public static Integer addByInteger(Integer i1, Integer i2) {
        if (i1 == null && i2 == null) return null;
        if (i1 == null) return i2;
        if (i2 == null) return i1;
        return i1 + i2;
    }

    /**
     * 两个Double类型值相加并保留num位小数
     * @param i1 加数1
     * @param i2 加数2
     * @param num 小数位数
     * @return 相加的和
     */
    public static Double addByDouble(Double i1, Double i2, int num) {
        if (i1 == null && i2 == null) return null;
        if (i1 == null) return toDouble(i2, num);
        if (i2 == null) return toDouble(i1, num);
        return (new BigDecimal(Double.toString(i1)).add(new BigDecimal(Double.toString(i2)))).setScale(num, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 判断是否为double的字符串
     * @param value 字符串
     * @return boolean
     */
    public static Boolean isDouble(String value) {
        if (StringUtil.isEmpty(value)) return false;
        try {
            Double.parseDouble(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}

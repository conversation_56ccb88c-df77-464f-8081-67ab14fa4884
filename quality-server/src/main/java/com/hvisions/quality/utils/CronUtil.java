package com.hvisions.quality.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 根据时间获取cron表达式
 * @date 2022/3/24 13:35
 */
public class CronUtil {

    /***
     * @Description 返回小时执行的cron表达式
     *
     * <AUTHOR>
     * @Date 2022-10-14 10:57
     * @param start 开始时刻
     * @param end   结束时刻
     * @param interval 间隔几小时小时
     * @return java.lang.String
     **/
    public static String getCron(Date start, Date end, int interval) throws ParseException {
        StringBuffer cron = new StringBuffer();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH");
        Integer s = Integer.parseInt(simpleDateFormat.format(start));
        Integer e = Integer.parseInt(simpleDateFormat.format(end));
        if (s == 0 && e == 23) {
            // 0 0 0/13 * * ? *
            cron.append("0 0 0/");
            cron.append(interval);
            cron.append(" * * ? *");
            return cron.toString();

        } else {
            // 0 0 2,13,15,19,20,22 * * ? *
            cron.append("0 0 ");
            cron.append(s);

            int temp = s;
            while ((temp + interval) <= e) {
                temp = temp + interval;
                cron.append(",");
                cron.append(temp);
            }
            cron.append(" * * ? *");
            return cron.toString();
        }
    }
}

package com.hvisions.quality.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.hvisions.common.component.HvStringDeserialzer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2023-05-18 09:45
 */
public class JacksonUtil {

	/**
	 * Date格式化字符串
	 */
	private static final String DATE_FORMAT = "yyyy-MM-dd";
	/**
	 * DateTime格式化字符串
	 */
	private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	/**
	 * Time格式化字符串
	 */
	private static final String TIME_FORMAT = "HH:mm:ss";

	public static Jackson2ObjectMapperBuilderCustomizer initJackson() {
		return builder -> {
			//自动对入参对象中的String进行trim操作。不需要此项功能的可以注释掉
			builder.deserializerByType(String.class, new HvStringDeserialzer());
			builder.serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATETIME_FORMAT)));
			builder.serializerByType(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));
			builder.serializerByType(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(TIME_FORMAT)));
			builder.deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DATETIME_FORMAT)));
			builder.deserializerByType(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));
			builder.deserializerByType(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(TIME_FORMAT)));
		};
	}

	/**
	 * 根据 Jackson2ObjectMapperBuilderCustomizer 构建 ObjectMapper
	 *
	 * @return ObjectMapper
	 */
	public static ObjectMapper getObjectMapper() {
		Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
		initJackson().customize(builder);
		return builder.build();
	}
}

package com.hvisions.quality.utils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;

/**
 * <AUTHOR>
 */
public class JsonTypeHandler2 extends BaseTypeHandler<Object> {

    /**
     * 这里是将传过来的 parameter 对象转成 json 格式的字符串
     * */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter,
                                    JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSONUtil.toJsonStr(parameter));
    }

    /**
     * 这里暂时是只能处理字符串转 int 数组
     * */
    @Override
    public Object getNullableResult(ResultSet resultSet, String columnName)
            throws SQLException {
        // 根据数据库字段获取对应的数据
        // 使用这个方法的时候只考虑了获取 开窖鉴定 的 酒醅_感官形态（fermentative_material_sense_form）这个字段
        // 这个字段是 varchar 类型，内容为 [1,2,3] 这种格式，数组为空的时候也得有 []
        String string = resultSet.getString(columnName);
        if (string == null) string = "[]";

        // 字符串先转字符数组，用来获取数组长度用的
        // 直接用 string 的长度也行，不过有可能会浪费太多内存空间
        String[] strings = string.substring(1, string.length() - 1).split(",");
        return JSON.parseArray(string).toArray(new Integer[strings.length]);
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {

        return JSONUtil.toList(rs.getString(columnIndex), Object.class);
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {

        return JSONUtil.toList(cs.getString(columnIndex), Object.class);
    }

}

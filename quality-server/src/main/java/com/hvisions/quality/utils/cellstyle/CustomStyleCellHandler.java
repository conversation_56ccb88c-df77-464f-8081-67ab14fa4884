package com.hvisions.quality.utils.cellstyle;

import com.alibaba.excel.write.handler.AbstractRowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @author: DengWeiTao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.quality.utils.cellstyle.test
 * @Date: 2022年07月27日 10:34
 * @Description:
 */
public class CustomStyleCellHandler extends AbstractRowWriteHandler {

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row
            , Integer relativeRowIndex, Boolean isHead) {
        Cell cell;
        Workbook workbook = row.getSheet().getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式
        cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        cellStyle.setBorderTop(BorderStyle.THIN); // 上边框
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderRight(BorderStyle.THIN); // 右边框
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12); // 字体
        cellStyle.setFont(font);

        // 获取excel表的第一行
        Row firstRow = row.getSheet().getRow(0);
        // 表头有多少个有效单元格就把样式从第一个单元格个填充到最后最后一个
        if (row.getCell(0) != null && row.getCell(0).getStringCellValue().contains("平均")) {
            for (int i = 0; i < firstRow.getLastCellNum(); i++) {
                cell = row.getCell(i) == null ? row.createCell(i) : row.getCell(i);
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);//设置前景填充样式
                cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());//前景填充色
                cell.setCellStyle(cellStyle);
            }

            // 获取当前行
            int currentRow = row.getRowNum();
            if (currentRow == 2) {
                // 表头占两行，从第三行开始写数据，如果第三行不存在数据的话直接退出
                return;
            }

            // 找到上一个内容为 '平均' 的单元格
            int avgRow = 2;
            for (int i = currentRow - 1; i < currentRow; i--) {
                // 获取前面行的第一个单元格
                Cell cellContain = row.getSheet().getRow(i).getCell(0);
                if (i <= 2) break;
                if (cellContain == null) return;
                if (cellContain.getStringCellValue().contains("平均")) {
                    avgRow = i + 1;
                    break;
                }
            }

            for (int i = 2; i < firstRow.getLastCellNum(); i++) {
                int colTotal = 0;
                int nums = 0;
                for (int j = avgRow; j < currentRow; j++) {
                    Cell dayOrHour = row.getSheet().getRow(j).getCell(i);
                    if (dayOrHour == null) continue;
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(dayOrHour);
                    if (value == null || value.equals("")) continue;
                    // 当前列的值相加
                    colTotal += new BigDecimal(value).intValue();
                    // 当前列不为空的个数
                    nums++;
                }
                if (nums > 0) {
                    cell = row.createCell(i);
                    // 四舍五入
                    cell.setCellValue(new BigDecimal(colTotal).divide(new BigDecimal(nums), 0, RoundingMode.HALF_UP).intValue());
                }
            }
        }

        // 设置每一行的每一个单元格的样式
        for (int i = 0; i < firstRow.getLastCellNum(); i++) {
            cell = row.getCell(i) == null ? row.createCell(i) : row.getCell(i);
            cell.setCellStyle(cellStyle);
        }
    }
}

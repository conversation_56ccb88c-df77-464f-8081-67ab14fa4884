package com.hvisions.quality.utils.cellmerge;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @author: Deng<PERSON>eiTao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.quality.utils.merge.MergeCellModel
 * @Date: 2022年07月25日 17:13
 * @Description: 合并单元格信息
 */
@Getter
@Setter
@NoArgsConstructor
public class MergeCellModel{

    /**
     * sheet名称
     */
    private String sheetName;

    /**
     * 开始行号
     */
    private int startRowIndex;

    /**
     * 开始列号
     */
    private int startColumnIndex;

    /**
     * 结束行号
     */
    private int endRowIndex;

    /**
     * 结束列号
     */
    private int endColumnIndex;
}

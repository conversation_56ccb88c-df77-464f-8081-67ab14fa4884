package com.hvisions.quality.feign;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        name = "printer713",
        path = "/print",
        fallbackFactory = Print713ClientFallBack.class
)
public interface Print713Client {

    @ApiOperation("打印")
    @PostMapping({"/print"})
    ResultVO print(@RequestBody PrintDTO printDTO);

}

package com.hvisions.quality.feign;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/13 15:28
 */

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import org.springframework.stereotype.Component;

@Component
public class Print718ClientFallBack extends BaseFallbackFactory<Print718Client> {
    public Print718ClientFallBack() {
    }

    public Print718Client getFallBack(ResultVO vo) {
        return new Print718Client() {
            public ResultVO print(PrintDTO printDTO) {
                return vo;
            }
        };

    }
}

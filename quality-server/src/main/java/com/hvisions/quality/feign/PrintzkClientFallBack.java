package com.hvisions.quality.feign;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/13 15:28
 */

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import org.springframework.stereotype.Component;

@Component
public class PrintzkClientFallBack extends BaseFallbackFactory<PrintzkClient> {
    public PrintzkClientFallBack() {
    }

    public PrintzkClient getFallBack(ResultVO vo) {
        return new PrintzkClient() {
            public ResultVO print(PrintDTO printDTO) {
                return vo;
            }
        };

    }
}

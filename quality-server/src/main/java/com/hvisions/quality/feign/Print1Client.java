package com.hvisions.quality.feign;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        name = "printer1",
        path = "/print",
        fallbackFactory = Print1ClientFallBack.class
)
public interface Print1Client {

    @ApiOperation("打印")
    @PostMapping({"/print"})
    ResultVO print(@RequestBody PrintDTO printDTO);

}

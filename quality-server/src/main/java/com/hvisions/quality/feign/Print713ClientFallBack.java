package com.hvisions.quality.feign;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/13 15:28
 */

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import org.springframework.stereotype.Component;

@Component
public class Print713ClientFallBack extends BaseFallbackFactory<Print713Client> {
    public Print713ClientFallBack() {
    }

    public Print713Client getFallBack(ResultVO vo) {
        return new Print713Client() {
            public ResultVO print(PrintDTO printDTO) {
                return vo;
            }
        };

    }
}

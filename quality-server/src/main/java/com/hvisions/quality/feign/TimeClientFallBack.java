package com.hvisions.quality.feign;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/13 15:28
 */

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;

@Component
public class TimeClientFallBack extends BaseFallbackFactory<TimeClient> {
    public TimeClientFallBack() {
    }

    public TimeClient getFallBack(ResultVO vo) {
        return new TimeClient() {


            @Override
            public ResultVO startTimer(int timerId) {
                return vo;
            }

            @Override
            public ResultVO stopTimer(int timerId) {
                return vo;
            }
        };
    }
}

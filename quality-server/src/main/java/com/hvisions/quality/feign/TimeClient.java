package com.hvisions.quality.feign;

import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "timer",
        path = "/timer",
        fallbackFactory = TimeClientFallBack.class
)
public interface TimeClient {

    @ApiOperation("根据timerId启动定时器")
    @PutMapping({"/startTimer"})
    ResultVO startTimer(@RequestParam(name = "timerId") int timerId);

    @PutMapping({"/stopTimer"})
    @ApiOperation("根据timerId停止定时器")
    ResultVO stopTimer(@RequestParam(name = "timerId") int timerId);

}

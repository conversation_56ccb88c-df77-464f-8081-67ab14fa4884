package com.hvisions.quality.feign;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/13 15:28
 */

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.print.dto.PrintDTO;
import org.springframework.stereotype.Component;

@Component
public class Print1ClientFallBack extends BaseFallbackFactory<Print1Client> {
    public Print1ClientFallBack() {
    }

    public Print1Client getFallBack(ResultVO vo) {
        return new Print1Client() {
            public ResultVO print(PrintDTO printDTO) {
                return vo;
            }
        };

    }
}

<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.WheatReportMapper">
<!--    获取小麦合格率基础数据-->
    <select id="getWheatRateBaseData"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.rate.WheatRateBaseDataDTO">
        SELECT ddd.delivery_number,ddd.material_code,ddd.material_name,ddd.inspect_state,v.`code` vendor_code,v.`name` vendor_name,DATE_FORMAT( deo.demand_date, '%Y-%m-%d' ) demand_date

        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0

        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3 AND ddd.inspect_state IN (2,3)
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT( deo.demand_date, '%Y-%m-%d' ) BETWEEN DATE_FORMAT( #{startDate}, '%Y-%m-%d' ) AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        </if>
        <if test="vendorCode != null and vendorCode != ''">
            AND v.code = #{vendorCode}
        </if>
    </select>

    <select id="getWheatSupplyPassRate"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.rate.WheatSupplyPassRateDTO">
        SELECT ddd.material_code,ddd.material_name,
            COUNT(1) vehicleNum,
            SUM(IF(ddd.inspect_state = 2,1,0)) passNum,
            SUM(IF(ddd.inspect_state = 3,1,0)) noPassNum,
            FORMAT(SUM(IF(ddd.inspect_state = 2,1,0))/COUNT(1)*100,0)  rate,
            <if test="type == 1">DATE_FORMAT(deo.demand_date,'%Y-%m-%d') demand_date</if>
            <if test="type == 2">DATE_FORMAT(deo.demand_date,'%Y-%m') demand_date</if>
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0

        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3 AND ddd.inspect_state IN (2,3)
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT( deo.demand_date, '%Y-%m-%d' ) BETWEEN DATE_FORMAT( #{startDate}, '%Y-%m-%d' ) AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        </if>
        <if test="vendorCode != null and vendorCode != ''">
            AND v.code = #{vendorCode}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND ddd.material_code = #{materialCode}
        </if>
        GROUP BY ddd.material_code,ddd.material_name,<if test="type == 1">DATE_FORMAT(deo.demand_date,'%Y-%m-%d')</if> <if test="type == 2">DATE_FORMAT(deo.demand_date,'%Y-%m')</if>
    </select>

    <select id="getWheatSupplyPassRateVendor"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.rate.WheatSupplyPassRateVendorDTO">

        SELECT v.`name` vendor_name,
            COUNT(1) vehicleNum,
            SUM(IF(ddd.inspect_state = 2,1,0)) passNum,
            SUM(IF(ddd.inspect_state = 3,1,0)) noPassNum,
            FORMAT(SUM(IF(ddd.inspect_state = 2,1,0))/COUNT(1)*100,0)  rate,
            <if test="type == 1">DATE_FORMAT(deo.demand_date,'%Y-%m-%d') demand_date</if>
            <if test="type == 2">DATE_FORMAT(deo.demand_date,'%Y-%m') demand_date</if>
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3 AND ddd.inspect_state IN (2,3)
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT( deo.demand_date, '%Y-%m-%d' ) BETWEEN DATE_FORMAT( #{startDate}, '%Y-%m-%d' ) AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        </if>
        <if test="vendorCode != null and vendorCode != ''">
            AND v.code = #{vendorCode}
        </if>
        GROUP BY v.`name` ,<if test="type == 1">DATE_FORMAT(deo.demand_date,'%Y-%m-%d')</if> <if test="type == 2">DATE_FORMAT(deo.demand_date,'%Y-%m')</if>
    </select>

    <select id="getMaterialVendorList"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.rate.WheatSupplyPassRateVendorDTO">
        SELECT v.`name` vendor_name,
        COUNT(1) vehicleNum,
        SUM(IF(ddd.inspect_state = 2,1,0)) passNum,
        SUM(IF(ddd.inspect_state = 3,1,0)) noPassNum,
        FORMAT(SUM(IF(ddd.inspect_state = 2,1,0))/COUNT(1)*100,0)  rate
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3 AND ddd.inspect_state IN (2,3)
        AND ddd.material_code =#{materialCode}  AND deo.demand_date LIKE concat('%',#{demandDate},'%')
        GROUP BY v.`name`
    </select>

    <select id="getWheatImpurityRemovalReport"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.vendorPurchase.WheatImpurityRemovalReportDTO">
        SELECT
        ddd.material_name,
        ddd.material_code,
        v.`code` vendor_code,
        v.`name` vendor_name,
        MIN(deo.demand_date) start_time,
        MAX(deo.demand_date) end_time,
        SUM(ddd.leave_net_weight) arrival_weight,
        SUM(ddd.impurity_weight) impurity_weight,
        SUM(ddd.net_weight) net_weight,
        sum(IF(posting_state = 1,ddd.net_weight,NULL)) posting_quantity,
        COUNT(ddd.id) deliveryNum,
        sum(IF(ddd.inspect_state = 3,1,NULL)) refuseNum
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.id = di.order_detail_id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_purchase_order po ON po.id = pod.order_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3
        <if test="materialCodes != null and materialCodes != ''">
            AND FIND_IN_SET(ddd.`material_code`,#{materialCodes})
        </if>
        <if test="vendorCodes != null and vendorCodes != ''">
            AND FIND_IN_SET(v.`code`,#{vendorCodes})
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>
        GROUP BY po.vendor_id, ddd.material_code,ddd.material_name
    </select>

    <select id="getWheatPurchaseOrderCompleteReport"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.purchase.WheatPurchaseOrderCompleteReportDTO">
        SELECT t.* ,
            SUM(ddd.leave_net_weight) arrival_weight,
            SUM(ddd.net_weight) net_weight,
            sum(IF(posting_state = 1,ddd.net_weight,NULL)) posting_quantity
            from (
            SELECT po.id, po.order_no,po.sap_order,
            any_value(pod.material_code) material_code,any_value(pod.material_name) material_name,v.`code` vendor_code,v.`name` vendor_name,
            SUM(pod.quantity_supplied ) quantity_supplied
            FROM t_mp_purchase_order po
            LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
            LEFT JOIN t_mp_purchase_order_detail pod ON pod.order_id = po.id AND pod.deleted = 0 AND pod.is_new = 1
            WHERE po.deleted = 0 AND pod.material_code IN ('11000566','11000565')
            <if test="materialCodes != null and materialCodes != ''">
                AND FIND_IN_SET(ddd.`material_code`,#{materialCodes})
            </if>
            <if test="vendorCodes != null and vendorCodes != ''">
                AND FIND_IN_SET(v.`code`,#{vendorCodes})
            </if>
            <if test="startTime != null and endTime != null">
                AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
                DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND
                DATE_FORMAT(#{endTime}, '%Y-%m-%d')
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND po.sap_order LIKE "%"#{orderNo}"%"
            </if>
            GROUP BY po.id
        ) t
        LEFT JOIN t_mp_daily_delivery dd ON dd.purchase_id = t.id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_delivery_item di ON di.delivery_id = dd.id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0 and ddd.type = 3
        GROUP BY t.id
        ORDER BY t.id DESC
    </select>

    <select id="getWheatPurchaseOrderDailyCompleteDetail"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.purchase.WheatPurchaseOrderDailyCompleteReportDTO">
        SELECT
	        deo.demand_date,pod.quantity_supplied,pod.unit_price,pod.unit_price_base,di.arrival_weight, di.posting_quantity,SUM(ddd.impurity_weight) impurity_weight

        FROM t_mp_delivery_item di
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.id = di.order_detail_id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_purchase_order po ON po.id = pod.order_id AND po.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0
        WHERE di.deleted = 0 AND po.id = #{purchaseOrderId}
        GROUP BY di.id
        ORDER BY deo.demand_date ASC
    </select>

    <select id="getWheatPurchaseArriveReport"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.purchaseArrive.WheatPurchaseArriveReportDTO">

        SELECT
            pod.material_id,pod.material_code,pod.material_name,v.`code` vendor_code,v.`name` vendor_name,
            IFNULL(SUM(pod.quantity_supplied ),0)  quantity_supplied,
             (select IFNULL(SUM(ddd.leave_net_weight),0)
                from t_mp_daily_delivery dd
                LEFT JOIN t_mp_delivery_item di ON di.delivery_id = dd.id AND di.deleted = 0
                LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0 and ddd.type = 3 AND ddd.material_code != '0000'
                LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
                where dd.purchase_id = po.id AND dd.deleted = 0
                <if test="startTime != null and endTime != null">
                    AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
                    DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND
                    DATE_FORMAT(#{endTime}, '%Y-%m-%d')
                </if>
                ) arrival_weight
        FROM t_mp_purchase_order po
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.order_id = po.id AND pod.deleted = 0 AND pod.is_new = 1
        WHERE po.deleted = 0 AND pod.material_code IN ('11000566','11000565')
            <if test="materialCodes != null and materialCodes != ''">
                AND FIND_IN_SET(pod.`material_code`,#{materialCodes})
            </if>
            <if test="vendorCodes != null and vendorCodes != ''">
                AND FIND_IN_SET(v.`code`,#{vendorCodes})
            </if>
        GROUP BY pod.material_id,pod.material_code,pod.material_name,v.`code`,v.`name`, arrival_weight
        ORDER BY pod.material_code DESC
    </select>

    <select id="getWheatPurchaseCountReport"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount.WheatPurchaseCountReportDTO">
        SELECT
            DATE_FORMAT(deo.demand_date, '%Y') year,
            ddd.material_code,
            ddd.material_name,
            v.`code` vendor_code,
            v.`name` vendor_name,
            COUNT(ddd.id) deliveryNum,
        sum(IF(it.associated_document is not NULL, 1, 0)) refuseNum
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.id = di.order_detail_id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_purchase_order po ON po.id = pod.order_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN (SELECT DISTINCT associated_document from t_qa_inspection_task WHERE inspection_result = '不合格' and deleted = 0) it on it.associated_document = ddd.delivery_number
        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3
        <if test="materialCodes != null and materialCodes != ''">
            AND FIND_IN_SET(ddd.`material_code`,#{materialCodes})
        </if>
        <if test="vendorCodes != null and vendorCodes != ''">
            AND FIND_IN_SET(v.`code`,#{vendorCodes})
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>
        GROUP BY ddd.material_code,ddd.material_name, po.vendor_id,DATE_FORMAT(deo.demand_date, '%Y')
        ORDER BY DATE_FORMAT(deo.demand_date, '%Y') ASC,ddd.material_code DESC
    </select>

    <select id="getWheatPurchaseCountMonthReport"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount.WheatPurchaseCountMonthReportDTO">
        SELECT
            DATE_FORMAT(deo.demand_date, '%c') month,
            FORMAT(100 * (COUNT(ddd.id) - sum(IF(it.inspection_result = '不合格', 1, 0))) / COUNT(ddd.id),0) passRate
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.id = di.order_detail_id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_purchase_order po ON po.id = pod.order_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN t_qa_inspection_task it on it.associated_document = ddd.delivery_number and it.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.material_code != '0000' AND ddd.type = 3
            AND DATE_FORMAT(deo.demand_date, '%Y') = #{year}
            AND ddd.material_code = #{materialCode}
            AND v.`code` = #{vendorCode}
        GROUP BY ddd.material_code,ddd.material_name, po.vendor_id,DATE_FORMAT(deo.demand_date, '%c')
        ORDER BY DATE_FORMAT(deo.demand_date, '%c') ASC,ddd.material_code DESC
    </select>
    <select id="getWheatCountReportBy2024"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount.WheatPurchaseCountReportDTO">
            select * from t_mp_wheat_count_report where 1=1 order by 'month'
    </select>
    <select id="getWheatCountReportDetailBy2024"
            resultType="com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount.WheatPurchaseCountDetailReportDTO">
            select * from t_mp_wheat_count_report_detail where 1=1
    </select>
</mapper>

<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.PurchaseScreenMapper">

    <!-- 执行中的采购订单列表 -->
    <select id="getExecutePurchaseOrderList" resultType="com.hvisions.purchase.dto.purchase.screen.PurchaseOrderExecuteDTO">
        SELECT a.* FROM (

        SELECT
        n1.id,
        n1.order_no,
        n1.sap_order,
        n2.`code` AS vendor_code,
        n2.`name` AS vendor_name,
        n7.material_code,
        n7.material_name,
        (
        SELECT SUM(pod.quantity_supplied)
        FROM t_mp_purchase_order_detail pod
        WHERE pod.deleted = 0 AND pod.order_id = n1.id AND pod.is_new = 1
        ) quantity_supplied,
        IFNULL( n7.arrival_weight, 0 ) arrival_weight,
        (
        SELECT COUNT( 1 )
        FROM t_mp_purchase_order_detail d
        LEFT JOIN t_mp_delivery_item it ON it.order_detail_id = d.id AND it.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail l ON l.delivery_item_id = it.id AND l.deleted = 0
        WHERE d.is_new = 1 AND d.order_id = n1.id AND d.deleted = 0 AND l.id IS NOT NULL AND l.inspect_state = "3"
        ) unqualified_vehicle

        FROM t_mp_purchase_order n1
        LEFT JOIN t_mp_vendor n2 ON n1.vendor_id = n2.id AND n2.deleted = 0
        LEFT JOIN (
        SELECT
        d.order_id,
        ANY_VALUE ( d.material_code ) material_code,
        ANY_VALUE ( d.material_name ) material_name,
        SUM( it.arrival_weight ) arrival_weight
        FROM t_mp_purchase_order_detail d
        LEFT JOIN t_mp_delivery_item it ON it.order_detail_id = d.id AND it.deleted = 0
        WHERE d.deleted = 0 AND d.is_new = 1
        GROUP BY d.order_id
        ) n7 ON n7.order_id = n1.id
        WHERE
        n1.deleted = 0 AND n1.state = 2
        ORDER BY id DESC
        ) a
        LEFT JOIN materials.hv_bm_material m ON m.material_code = a.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        WHERE 1 = 1
        <if test="materialTypeCodeList != null and materialTypeCodeList.size() > 0">
            AND mt.material_type_code IN
            <foreach collection="materialTypeCodeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>

<!--    获取送货车辆数量-->
    <select id="getVehicleQuantity" resultType="com.hvisions.purchase.dto.purchase.screen.VehicleQuantityDTO">

         SELECT
             mt.material_type_code,
             SUM(1) planQuantity,
             SUM(IF(ddd.state = 0,1,0)) waitEnterQuantity,
             SUM(IF(ddd.inspect_state=2,1,0)) qualifiedQuantity,
             SUM(IF(ddd.state =4 AND ddd.inspect_state=2,1,0)) alreadyUnloading

        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON m.material_code = ddd.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type

        WHERE ddd.deleted = 0 and ddd.`material_name` != "丢糟"  AND mt.material_type_code IS NOT NULL
            AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') =  DATE_FORMAT(NOW(), '%Y-%m-%d')

        GROUP BY mt.material_type_code
    </select>

<!--    获取物料送货信息-->
    <select id="getMaterialQuantity" resultType="com.hvisions.purchase.dto.purchase.screen.MaterialQuantityDTO">
        SELECT
            mt.material_type_code,
            SUM(dd.deliver_quantity) deliver_quantity,
            SUM(di.inbound_weight) inbound_weight

        FROM t_mp_daily_delivery dd
        LEFT JOIN t_mp_delivery_item di ON di.updater_id = dd.id AND di.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON m.material_code = dd.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type

        WHERE dd.deleted = 0 AND mt.material_type_code IS NOT NULL AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') =  DATE_FORMAT(NOW(), '%Y-%m-%d')
        GROUP BY mt.material_type_code
    </select>

    <select id="getGLPhaseII" resultType="com.hvisions.purchase.dto.purchase.screen.InPhaseIIDTO"  >

        SELECT l1.id,l1.`code`,l1.`name`,SUM(ddd.net_weight/1000) net_weight
        FROM brewage_rawmaterial_production.t_mpd_rl_management l1
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management l2 ON l1.parent_id = l2.id AND l2.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail ddd ON
                ddd.warehouse_id = l1.id AND ddd.deleted = 0 AND ddd.state = 9
                AND DATE_FORMAT(ddd.update_time, '%Y-%m-%d') =  DATE_FORMAT(NOW(), '%Y-%m-%d')
        WHERE l2.`code` = "GL02" AND l1.deleted = 0
        GROUP BY l1.id
    </select>

    <select id="getDKPhaseII" resultType="com.hvisions.purchase.dto.purchase.screen.InPhaseIIDTO"  >

        SELECT l1.id,l1.`code`,l1.`name`,SUM(ddd.net_weight/1000) net_weight
        FROM brewage_rawmaterial_production.t_mpd_rl_management l1
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management l2 ON l1.parent_id = l2.id AND l2.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail ddd ON
                ddd.warehouse_id = l1.id AND ddd.deleted = 0 AND ddd.state = 9
                AND DATE_FORMAT(ddd.update_time, '%Y-%m-%d') =  DATE_FORMAT(NOW(), '%Y-%m-%d')
        WHERE l2.`code` = "DK02" AND l1.deleted = 0
        GROUP BY l1.id
    </select>

    <select id="getGLPhaseIIStock" resultType="com.hvisions.purchase.dto.purchase.screen.InPhaseIIDTO"  >
        SELECT l1.id,l1.`code`,l1.`name`,ROUND(SUM(wd.stock_quantity/1000)) net_weight
        FROM brewage_rawmaterial_production.t_mpd_rl_management l1
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management l2 ON l1.parent_id = l2.id AND l2.deleted = 0
        LEFT JOIN brewage_rawmaterial_production.t_mpd_warehouse_data wd ON l1.id = wd.storage_id AND wd.deleted = 0
        WHERE l2.`code` = "GL02" AND l1.deleted = 0
        GROUP BY l1.id
    </select>

    <select id="getDKPhaseIIStock" resultType="com.hvisions.purchase.dto.purchase.screen.InPhaseIIDTO"  >
        SELECT l1.id,l1.`code`,l1.`name`,ROUND(SUM(wd.stock_quantity/1000)) net_weight
        FROM brewage_rawmaterial_production.t_mpd_rl_management l1
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management l2 ON l1.parent_id = l2.id AND l2.deleted = 0
        LEFT JOIN brewage_rawmaterial_production.t_mpd_warehouse_data wd ON l1.id = wd.storage_id AND wd.deleted = 0
        WHERE l2.`code` = "DK02" AND l1.deleted = 0
        GROUP BY l1.id
    </select>

    <select id="getGLPhaseIStock" resultType="com.hvisions.purchase.dto.purchase.screen.InPhaseIIDTO"  >
        SELECT l1.id,l1.`code`,l1.`name`,ROUND(SUM(wd.stock_quantity/1000)) net_weight
        FROM brewage_rawmaterial_production.t_mpd_rl_management l1
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management l2 ON l1.parent_id = l2.id AND l2.deleted = 0
        LEFT JOIN brewage_rawmaterial_production.t_mpd_warehouse_data wd ON l1.id = wd.storage_id AND wd.deleted = 0
        WHERE l2.`code` = "GL01" AND l1.deleted = 0
        GROUP BY l1.id
    </select>

    <select id="getDKPhaseIStock" resultType="com.hvisions.purchase.dto.purchase.screen.InPhaseIIDTO"  >
        SELECT l1.id,l1.`code`,l1.`name`,ROUND(SUM(wd.stock_quantity/1000)) net_weight
        FROM brewage_rawmaterial_production.t_mpd_rl_management l1
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management l2 ON l1.parent_id = l2.id AND l2.deleted = 0
        LEFT JOIN brewage_rawmaterial_production.t_mpd_warehouse_data wd ON l1.id = wd.storage_id AND wd.deleted = 0
        WHERE l2.`code` = "DK01" AND l1.deleted = 0
        GROUP BY l1.id
    </select>

    <select id="getReceiveQuantity" resultType="com.hvisions.purchase.dto.purchase.screen.ReceiveQuantityDetailDTO">
        SELECT
            deo.demand_date `date`,mt.material_type_code,
            SUM(dd.deliver_quantity) planQuantity,
            SUM(di.inbound_weight) receiveQuantity,
            FORMAT(SUM(di.inbound_weight)/SUM(dd.deliver_quantity),2) rate

        FROM t_mp_daily_delivery dd
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON dd.material_code = m.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        LEFT JOIN (
                SELECT di.delivery_id,SUM(di.inbound_weight) inbound_weight
                FROM t_mp_delivery_item di
                WHERE di.deleted = 0
                GROUP BY di.delivery_id
        ) di ON di.delivery_id = dd.id
        WHERE dd.deleted = 0 AND deo.demand_date BETWEEN DATE_FORMAT(DATE_SUB(now(),interval 10 day),"%Y-%m-%d") AND DATE_FORMAT(NOW(),"%Y-%m-%d")

        GROUP BY deo.demand_date,mt.material_type_code
    </select>


    <select id="getDriveReceiveQuantity" resultType="com.hvisions.purchase.dto.purchase.screen.MaterialQuantityDTO">
        SELECT a.material_type_code,
            SUM(a.demand_quantity)/1000 demand_quantity,
            SUM(a.arrival_weight)/1000 arrival_weight,
            SUM(a.inbound_weight)/1000 inbound_weight,
            FORMAT(SUM(a.inbound_weight)/SUM(a.demand_quantity),2) complete_ratio

        FROM (
            SELECT dm.id,ANY_VALUE(mt.material_type_code) material_type_code,

                SUM(dm.demand_quantity) demand_quantity,
                SUM(di.arrival_weight) arrival_weight,
                SUM(di.inbound_weight) inbound_weight
            FROM t_mp_demand_order dm
            LEFT JOIN materials.hv_bm_material m ON m.material_code = dm.material_code
            LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
            LEFT JOIN t_mp_daily_delivery dd ON dd.demand_id = dm.id AND dd.deleted = 0
            LEFT JOIN t_mp_delivery_item di ON di.delivery_id = dd.id AND di.deleted = 0

            WHERE dm.deleted = 0
            <if test="type == 0">
              and  DATE_FORMAT(dm.demand_date,'%Y-%m-%d') BETWEEN DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) DAY) AND

                DATE_ADD(CURDATE(),INTERVAL 6-WEEKDAY(CURDATE()) DAY)
            </if>
            <if test="type == 1">
                AND DATE_FORMAT(dm.demand_date,'%Y-%m-%d') BETWEEN date_add(curdate(),interval -day(curdate())+1 day) AND last_day(curdate())
            </if>
            GROUP BY dm.id
        )a
        GROUP BY a.material_type_code

    </select>
<!--    获取今天收货批次合格率-->
    <select id="getNowReceiveBatch" resultType="com.hvisions.purchase.dto.purchase.screen.DriveReceiveBatchDTO">
        SELECT mt.material_type_code,
            COUNT(ddd.id) total,
            FORMAT(SUM(CASE ddd.inspect_state WHEN 2 THEN	1	END)/COUNT(ddd.id) ,2) ratio
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order dm ON dm.id = dd.demand_id AND dm.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON m.material_code = dm.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        WHERE ddd.deleted = 0 AND DATE_FORMAT(dm.demand_date,'%Y-%m-%d') = CURDATE()
        GROUP BY mt.material_type_code
    </select>

<!--    获取当月采购合格率-->
    <select id="getMonthPassRate" resultType="com.hvisions.purchase.dto.purchase.screen.DriveReceiveBatchDTO">
        SELECT DATE_FORMAT(dm.demand_date,'%Y-%c-%e') demand_date, mt.material_type_code,
            COUNT(ddd.id) total,
            FORMAT(SUM(CASE ddd.inspect_state WHEN 2 THEN	1	END)/COUNT(ddd.id) ,2) ratio
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order dm ON dm.id = dd.demand_id AND dm.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON m.material_code = dm.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        WHERE ddd.deleted = 0
                AND DATE_FORMAT(dm.demand_date,'%Y-%m-%d') BETWEEN date_add(curdate(),interval -day(curdate())+1 day) AND last_day(curdate())
        GROUP BY DATE_FORMAT(dm.demand_date,'%Y-%c-%e'), mt.material_type_code
        ORDER BY DATE_FORMAT(dm.demand_date,'%Y-%c-%e') ASC
    </select>

    <select id="getIssueMaterialQuantity" resultType="com.hvisions.purchase.dto.purchase.screen.DriveMaterialIssueDTO">

    	SELECT 'DK' material_type,IFNULL(SUM(a.plan_quantity),0)/1000 plan_quantity,IFNULL(SUM(a.actual_quantity),0)/1000 actual_quantity
        FROM (
            SELECT o.plan_quantity,SUM(bi.actual_quantity) actual_quantity
            FROM brewage_rawmaterial_production.`t_mpd_bi_order_detail` bi
            LEFT JOIN brewage_rawmaterial_production.t_mpd_bran_issue_order o ON o.id = bi.order_id AND o.deleted = 0
            WHERE bi.deleted = 0 AND DATE_FORMAT(bi.create_time,'%Y-%m-%d') = CURDATE() AND o.center_id = #{centerId}
            GROUP BY bi.order_id
        )a
        UNION ALL

        SELECT 'GL' material_type,IFNULL(SUM(a.plan_quantity),0)/1000 plan_quantity,IFNULL(SUM(a.actual_quantity),0)/1000 actual_quantity
        FROM (
            SELECT o.plan_quantity,SUM(sd.actual_quantity) actual_quantity
            FROM brewage_rawmaterial_production.`t_mpd_sd_order_detail` sd
            LEFT JOIN brewage_rawmaterial_production.t_mpd_sorghum_dispense_order o ON o.id = sd.order_id AND o.deleted = 0
            WHERE sd.deleted = 0 AND DATE_FORMAT(sd.create_time,'%Y-%m-%d') = CURDATE() AND o.center_id = #{centerId}
            GROUP BY sd.order_id
        )a

        UNION ALL

        SELECT 'QF' material_type,IFNULL(SUM(iod.bucket_weight),0)/1000 actual_quantity,
			(
			    SELECT IFNULL(SUM(t1.qu_powder),0)/1000 AS plan_quantity
                FROM t_po_workshop_pit_day_plan_material_request t1
                WHERE
                    t1.is_deleted = 0 AND
                    t1.center = #{centerId} AND
                    date_format(t1.plan_date, '%Y-%m-%d') = CURDATE()
            ) plan_quantity
        FROM t_wp_issue_order_detail iod
        WHERE iod.deleted = 0 AND DATE_FORMAT(iod.accept_time,'%Y-%m-%d') = CURDATE() AND iod.center_id = #{centerId}

        UNION ALL

        SELECT
         'HJ' material_type,
         IFNULL( SUM( ti.degree_sixty_quantity ), 0 )/ 1000 + IFNULL( SUM( ti.degree_sixteen_eight_quantity ), 0 )/ 1000 actual_quantity,
         IFNULL( SUM( it.require_quantity ), 0 )/ 1000 AS plan_quantity
        FROM
         t_bw_task_detail ti
         INNER JOIN t_bw_task ta ON ta.id = ti.task_id
         INNER JOIN t_bw_task_item it ON ta.id = it.task_id
        WHERE
         ti.deleted = 0
         AND DATE_FORMAT( ti.issued_begin_time, '%Y-%m-%d' ) = CURDATE()
         AND ti.center_id = #{centerId}

    </select>

    <select id="getYearInspectionRate" resultType="com.hvisions.purchase.dto.purchase.screen.DriveYearInspectionRateDetailDTO">
        SELECT
            DATE_FORMAT(it.actual_inspection_time,'%Y-%c') month,
            COUNT(it.id) total,
            SUM(CASE it.inspection_result WHEN "合格" THEN 1 END) qualifiedNum,
            SUM(CASE it.inspection_result WHEN "合格" THEN 1 END) / COUNT(it.id)*100 rate
        FROM t_qa_inspection_task it
        LEFT JOIN t_qa_standard_scene ss ON  ss.id = it.standard_scenes_id AND ss.deleted = 0
        LEFT JOIN t_qa_scene s ON s.id = ss.scenes_id AND s.deleted = 0

        WHERE it.deleted = 0 AND it.state = 9 AND it.inspection_result IS NOT NULL
            AND it.actual_inspection_time IS NOT NULL
            AND s.scene_name LIKE concat('%',#{sceneName},'%')
            AND DATE_FORMAT(it.actual_inspection_time,'%Y') = DATE_FORMAT(NOW(),"%Y")
        GROUP BY DATE_FORMAT(it.actual_inspection_time,'%Y-%c')

    </select>

    <select id="getMonthInspectionData" resultType="com.hvisions.purchase.dto.purchase.screen.DriveMonthInspectionDataDetailDTO">

        SELECT
            DATE_FORMAT(it.actual_inspection_time,'%Y-%c') month,
            id.indicator_name,
            FORMAT(SUM(id.`value`) / COUNT(id.`value`) ,2)  avg_value

        FROM t_qa_inspection_data id
        LEFT JOIN t_qa_inspection_task it ON it.id = id.inspection_id
        LEFT JOIN t_qa_standard_scene ss ON  ss.id = it.standard_scenes_id AND ss.deleted = 0
        LEFT JOIN t_qa_scene s ON s.id = ss.scenes_id AND s.deleted = 0
        WHERE
            it.deleted = 0 AND it.state = 9 AND it.actual_inspection_time IS NOT NULL AND
            id.deleted = 0 AND id.entry_method = 1 AND  id.type = 0 AND it.inspection_type = 0 AND s.scene_name LIKE concat('%',#{sceneName},'%')

	    GROUP BY id.indicator_name, DATE_FORMAT(it.actual_inspection_time,'%Y-%c')
    </select>

    <select id="getNowMaterialStock" resultType="com.hvisions.purchase.dto.purchase.screen.MaterialStockDTO">
        SELECT mt.material_type_code,SUM(wd.stock_quantity)/1000 stock_quantity
        FROM brewage_rawmaterial_production.t_mpd_warehouse_data wd
        LEFT JOIN materials.hv_bm_material m ON wd.material_code = m.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
        WHERE wd.deleted = 0
        GROUP BY mt.material_type_code
    </select>

</mapper>

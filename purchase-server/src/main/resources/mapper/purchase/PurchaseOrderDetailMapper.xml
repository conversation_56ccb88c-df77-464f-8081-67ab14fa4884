<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.PurchaseOrderDetailMapper">

    <!-- 根据采购单id或采购单详情id，获取已到货重量 -->
    <select id="getArrivalWeight" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(i.arrival_weight), 0)
        FROM t_mp_purchase_order_detail d
        LEFT JOIN t_mp_delivery_item i ON i.order_detail_id = d.id
        where d.is_new = "1"
        <if test="orderId != null">
            AND d.order_id = #{orderId}
        </if>
        <if test="orderDetailId != null">
            AND d.id = #{orderDetailId}
        </if>
    </select>

    <!-- 根据采购单id获取采购总数 -->
    <select id="getQuantitySupplied" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(quantity_supplied), 0) FROM t_mp_purchase_order_detail WHERE is_new = "1" and order_id = #{orderId}
    </select>

    <select id="getPurchaseOrderDetail" resultType="com.hvisions.purchase.dto.purchase.order.detail.PurchaseOrderDetailDTO">
        SELECT
            n1.id,
            n1.material_code,
            n1.material_name,
            n1.quantity_supplied,
            n1.unit_price,
            n1.unit_price_base,
            n1.remark,
            n1.uom,
            di.arrival_weight,
            di.inbound_weight,
            di.posting_quantity,
            l.`code` AS location_code,
            l.`name` AS location_name,
            cm.factory_code,
            cm.factory_name
        FROM
            t_mp_purchase_order_detail n1
            LEFT JOIN t_mp_purchase_order n2 ON n2.id = n1.order_id
            LEFT JOIN t_mp_procurement_planning n3 ON n2.planning_id = n3.id
            LEFT JOIN t_mp_inventory_location l ON n3.location_id = l.id
            LEFT JOIN (
                SELECT it.order_detail_id, SUM( it.inbound_weight ) inbound_weight, SUM( it.arrival_weight ) arrival_weight, SUM( it.posting_quantity ) posting_quantity
                FROM t_mp_delivery_item it
                GROUP BY it.order_detail_id
            ) di ON n1.id = di.order_detail_id
            LEFT JOIN t_mp_code_maintenance cm ON n2.maintenance_id = cm.id
        WHERE n1.num = #{num} and n1.order_id = #{orderId}
      </select>



</mapper>

<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.DailyDeliveryDetailMapper">

    <update id="updateSyncStatus">
        UPDATE t_mp_daily_delivery_detail
        SET sync_state = #{state}
        WHERE delivery_number = #{deliveryNumber}
    </update>

    <select id="getDzSerialNumber" resultType="java.lang.String">
        SELECT LPAD(count(1) + 1, IFNULL(4, 4), 0)
        FROM t_mp_daily_delivery_detail ddd
        WHERE ddd.delivery_number LIKE "%LZLJDZCL%" AND DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d')

    </select>

    <select id="getDemandDateByVehicleId" resultType="java.util.Date">
        SELECT deo.demand_date
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        WHERE ddd.id = #{vehicleId}
    </select>

    <select id="getVendorCodeByVehicleId" resultType="java.lang.String">
        SELECT v.`code` vendor_code
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        WHERE ddd.id = #{vehicleId}
    </select>

    <!-- 分页查询送货车辆列表 -->
    <select id="getDeliveryVehiclePageList"
            resultType="com.hvisions.purchase.dto.purchase.receiving.DeliveryVehiclePageDTO">
        SELECT ddd.*,dd.plan_number,deo.order_no,deo.demand_date,il.`name` location_name,
        ed.exception_data,po.vendor_id,v.`name` vendor_name,v.`code` vendor_code,it.*,mt.material_type_code,
        b.samplingCodes,b.inspectionIds,ur.create_time unload_time,u.user_name unload_name, it.actual_inspection_time,
        ddi.inspect_result, ddi.submit_user, ddi.inspect_fail_reason, ddi.description, ddi.pic_urls
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_daily_delivery_inspect ddi ON ddi.delivery_detail_id = ddd.id AND ddi.deleted = 0
        LEFT JOIN t_mp_inventory_location il ON il.id = deo.location_id AND il.deleted = 0
        LEFT JOIN t_mp_exception_data ed ON ed.id = ddd.exception_id AND ed.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN t_mp_unload_record ur ON ur.vehicle_id = ddd.id AND ur.deleted = 0
        LEFT JOIN authority.sys_user u ON ur.creator_id = u.id
        LEFT JOIN materials.hv_bm_material m ON m.material_code = ddd.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        LEFT JOIN (
        SELECT it.id inspection_id,it.inspection_order,it.inspection_result quality_result,it.sampling_id,it.sampling_code
        ,it.associated_document,it.inspection_remark,it.actual_inspection_time
        FROM t_qa_inspection_task it
        WHERE it.deleted = 0 and it.id IN (SELECT MAX(i.id) id FROM t_qa_inspection_task i where i.deleted = 0 GROUP BY i.associated_document)
        ) it ON it.associated_document = ddd.delivery_number
        LEFT JOIN (
            SELECT ddd.id ,GROUP_CONCAT(it.sampling_code) samplingCodes,GROUP_CONCAT(it.id) inspectionIds
            FROM t_mp_daily_delivery_detail ddd
            LEFT JOIN t_qa_inspection_task it  ON it.associated_document = ddd.delivery_number
            WHERE ddd.deleted = 0 AND it.deleted = 0
            GROUP BY ddd.id
        ) b ON b.id = ddd.id
        WHERE ddd.deleted = 0 and ddd.`material_name` != "丢糟"
        <if test="deliveryNumber != null and deliveryNumber != ''">
            AND ddd.`delivery_number` LIKE concat('%',#{deliveryNumber},'%')
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND ddd.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="notContainZZ == true">
            AND ddd.`material_code` != "0000"
        </if>
        <if test="materialId != null">
            AND ddd.`material_id` = #{materialId}
        </if>
        <if test="materialTypeCodeList != null and materialTypeCodeList.size() > 0">
            AND mt.material_type_code IN
            <foreach collection="materialTypeCodeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="typeList != null and typeList.size() > 0">
            AND ddd.type IN
            <foreach collection="typeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="inspectState != null">
            AND ddd.`inspect_state` = #{inspectState}
        </if>
        <if test="states != null">
            AND ddd.`state` in
            <foreach collection="states" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="planNumber != null and planNumber != ''">
            AND dd.`plan_number` LIKE concat('%',#{planNumber},'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND deo.order_no LIKE concat('%',#{orderNo},'%')
        </if>
        <if test="qualityResult != null and qualityResult != ''">
            AND it.quality_result = #{qualityResult}
        </if>
        <if test="vendorId != null">
            AND po.vendor_id = #{vendorId}
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY
        <if test="orderByInspect != null and orderByInspect == 1 ">
            it.actual_inspection_time,
        </if>
        deo.demand_date desc, ddd.id
    </select>

    <select id="getSamplingCodesByDeliveryNumber"
            resultType="java.lang.String">
        SELECT GROUP_CONCAT(t.sampling_code)
        FROM t_qa_inspection_task t
        WHERE t.deleted = 0
        <if test="deliveryNumber != null and deliveryNumber != ''">
            AND t.associated_document = #{deliveryNumber}
        </if>
        GROUP BY t.associated_document
    </select>

    <select id="getDzVehiclePageList"
            resultType="com.hvisions.purchase.dto.purchase.receiving.DeliveryVehiclePageDTO">
        SELECT ddd.*
        FROM `t_mp_daily_delivery_detail` ddd
        WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟"
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="receiptPlace != null and receiptPlace != ''">
            AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>

        <if test="appearanceStartTime != null and appearanceEndTime != null">
            AND DATE_FORMAT(ddd.appearance_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{appearanceStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{appearanceEndTime}, '%Y-%m-%d')
        </if>
        ORDER BY ddd.id DESC
    </select>

    <select id="getDzNetWeightSum"
            resultType="java.lang.String">
        SELECT SUM(ddd.net_weight)
        FROM `t_mp_daily_delivery_detail` ddd
        WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟"
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="receiptPlace != null and receiptPlace != ''">
            AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
    </select>


    <!--    根据采购单id获取采购单下的未过账送货单个数-->
    <select id="getAwaitPostingNum" resultType="java.lang.Integer">
        SELECT COUNT(ddd.id)
        FROM t_mp_purchase_order po
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.order_id = po.id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_delivery_item di ON di.order_detail_id = pod.id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0
        WHERE po.deleted = 0 AND ddd.posting_state = 0 AND po.id =#{purchaseOrderId}
    </select>

    <!--    根据送货单id获取采购单-->
    <select id="getPurchaseOrderByDeliveryDetailId" resultType="com.hvisions.purchase.purchase.entity.TMpPurchaseOrder">
        SELECT po.*
        FROM t_mp_purchase_order po
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.order_id = po.id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_delivery_item di ON di.order_detail_id = pod.id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0
        WHERE po.deleted = 0 and ddd.id = #{id}
        LIMIT 1
    </select>

    <!-- 根据日送货计划详情id获取供应商id -->
    <select id="getVendorByDetailId" resultType="com.hvisions.purchase.dto.purchase.vendor.VendorPageDTO">
        SELECT v.*
        FROM t_mp_daily_delivery_detail d
        LEFT JOIN t_mp_delivery_item di ON d.delivery_item_id = di.id
        LEFT JOIN t_mp_daily_delivery dd ON di.delivery_id = dd.id
        LEFT JOIN t_mp_purchase_order po ON dd.purchase_id = po.id
        LEFT JOIN t_mp_vendor v ON po.vendor_id = v.id
        WHERE d.id = #{id}
    </select>

    <!-- 根据物料类型获取送货车辆物料报表信息 -->
    <select id="getDeliveryVehicleInfo"
            resultType="com.hvisions.purchase.dto.purchase.report.DeliveryVehicleInfoDTO">
        SELECT
        d.id,
        d.license_plate_number,
        d.delivery_number,
        d.create_time,
        d.material_name,
        v.`name` AS vendor_name,
        d.gross_weight,
        d.appearance_weight,
        d.leave_net_weight,
        d.buckle_weight,
        d.impurity_weight,
        d.net_weight,
        it.inspection_id,
        it.state,
        it.quality_result,
        it.quality_remark,
        it.sampling_code,
        it.inspection_order,
        it.associated_document,
        it.inspection_name,
        it.remark,
        it.actual_inspection_time,
        it.actual_date,
        it.sampling_people,
        it.receive_data,
        it.final_result,
        (SELECT GROUP_CONCAT(t.sampling_code) samplingCodes
        FROM t_qa_inspection_task t
        WHERE t.deleted = 0
        AND t.associated_document = d.delivery_number
        GROUP BY t.associated_document) samplingCodes
        FROM
        t_mp_daily_delivery_detail d
        LEFT JOIN t_mp_delivery_item di ON d.delivery_item_id = di.id and di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON di.delivery_id = dd.id and dd.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON dd.purchase_id = po.id and po.deleted = 0
        LEFT JOIN t_mp_vendor v ON po.vendor_id = v.id and v.deleted = 0
        LEFT JOIN (
        SELECT
        it.id AS inspection_id,
        it.state,
        it.inspection_result AS quality_result,
        it.inspection_remark AS quality_remark,
        it.sampling_code,
        it.inspection_order,
        it.associated_document,
        it.inspection_name,
        it.inspection_remark remark,
        it.actual_inspection_time,
        s.actual_date,
        s.sampling_people,
        it.receive_data,
        CASE
        WHEN d.final_result = 0 THEN '通过'
        WHEN d.final_result = 1 THEN '驳回'
        WHEN d.final_result = 2 THEN '退货'
        WHEN d.final_result = 3 THEN '复检'
        WHEN d.final_result = 4 THEN '报废'
        WHEN d.final_result = 5 THEN '重蒸'
        ELSE ''
        END as final_result
        FROM t_qa_inspection_task it
        LEFT JOIN t_qa_sampling s ON it.sampling_id = s.id AND s.deleted = 0
        LEFT JOIN (
        SELECT inspection_id, final_result
        FROM (
        SELECT
        inspection_id,
        final_result,
        ROW_NUMBER() OVER (PARTITION BY inspection_id ORDER BY id DESC) AS rn
        FROM t_qa_disqualified
        ) t
        WHERE t.rn = 1
        ) d ON it.id = d.inspection_id
        WHERE it.deleted = 0
        AND it.inspection_type = 0
        AND it.state = 9
        ) it ON it.associated_document = d.delivery_number
        WHERE d.deleted = 0 and
        d.material_id IN (
        SELECT m.id
        FROM materials.hv_bm_material m
        WHERE m.material_type = ( SELECT t.id FROM materials.hv_bm_material_type t WHERE t.material_type_code = #{materialType} )
        )
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND d.license_plate_number LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND d.material_name LIKE concat('%',#{materialName},'%')
        </if>
        <if test="vendorName != null and vendorName != ''">
            AND v.`name` LIKE concat('%',#{vendorName},'%')
        </if>
        <if test="samplingCode != null and samplingCode != ''">
            AND it.sampling_code LIKE concat('%',#{samplingCode},'%')
        </if>
        <if test="state != null and state != ''">
            AND it.state = #{state}
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(it.actual_inspection_time, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="samplingStartDate != null and samplingEndDate != null">
            AND DATE_FORMAT(it.actual_date, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{samplingStartDate}, '%Y-%m-%d') AND DATE_FORMAT(#{samplingEndDate}, '%Y-%m-%d')
        </if>
        ORDER BY it.actual_date
        <if test="exportSort == null">
            DESC
        </if>
        <if test="exportSort != null and exportSort == 1 ">
            ASC
        </if>
    </select>

    <!-- 获取送货车辆报表 -->
    <select id="getVehicleReport" resultType="com.hvisions.purchase.dto.purchase.report.VehicleReportDTO">
        SELECT
            ddd.material_code,ddd.material_name,ddd.license_plate_number,ddd.estimated_number,ddd.admission_time,ddd.appearance_time,
            dd.plan_number,v.`code` vendor_code,v.`name` vendor_name,po.sap_order,po.contract_number,
            CASE ddd.state
            WHEN 0 THEN
            "未入场"
            WHEN 1 THEN
            "已入场"
            WHEN 2 THEN
            "可卸货"
            WHEN 3 THEN
            "卸货完成"
            WHEN 4 THEN
            "出门"
            WHEN 5 THEN
            "待处理"
            WHEN 6 THEN
            "拒收"
            WHEN 7 THEN
            "复检"
            WHEN 8 THEN
            "特殊退货"
            WHEN 9 THEN
            "已收货"
            END as state,
            CASE ddd.inspect_state
            WHEN 0 THEN
            "待检验"
            WHEN 1 THEN
            "质检中"
            WHEN 2 THEN
            "合格"
            WHEN 3 THEN
            "不合格"
            END as inspect_state,
            it.quality_result,it.inspection_order,
            it.rawMaterialsCommit,
            ddd.gross_weight,ddd.appearance_weight,ddd.buckle_weight,ddd.net_weight, IF(posting_state =1,ddd.net_weight,0) posting_quantity,
            (SELECT GROUP_CONCAT(c.disqualified_content) disqualified_content
            FROM t_qa_disqualified di
            LEFT JOIN t_qa_disqualified_content c on FIND_IN_SET(c.id,di.disqualified_count_ids)
            WHERE di.inspection_id = it.inspection_id AND di.deleted = 0
            GROUP BY di.inspection_id) defectiveReason
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN (
        SELECT it.id inspection_id,it.inspection_order,it.associated_document,it.inspection_result quality_result,it.inspection_remark rawMaterialsCommit
        FROM t_qa_inspection_task it
        WHERE it.deleted = 0 AND it.id IN (SELECT MAX(i.id) id FROM t_qa_inspection_task i GROUP BY
        i.associated_document)
        ) it ON it.associated_document = ddd.delivery_number
        WHERE ddd.deleted = 0 and ddd.`material_name` != "丢糟"
        and !LOCATE('杂质', ddd.`material_name`)
        <if test="materialCodes != null and materialCodes != ''">
            AND FIND_IN_SET(ddd.`material_code`,#{materialCodes})
        </if>
        <if test="types != null and types != ''">
            AND FIND_IN_SET(ddd.`type`,#{types})
        </if>
        <if test="vendorCodes != null and vendorCodes != ''">
            AND FIND_IN_SET(v.`code`,#{vendorCodes})
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="admissionStartTime != null and admissionEndTime ">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
                DATE_FORMAT(#{admissionStartTime}, '%Y-%m-%d') AND
                DATE_FORMAT(#{admissionEndTime}, '%Y-%m-%d')
        </if>
        <if test="planNumber != null and planNumber != ''">
            AND dd.plan_number LIKE concat('%',#{planNumber},'%')
        </if>
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="inspectState != null">
            AND ddd.`inspect_state` = #{inspectState}
        </if>
        <if test="postingState != null">
            AND ddd.`posting_state` = #{postingState}
        </if>
        <if test="inspectionOrder != null and inspectionOrder != ''">
            AND it.inspection_order LIKE concat('%',#{inspectionOrder},'%')
        </if>
        ORDER BY ddd.create_time desc
    </select>

    <!-- 过磅记录-->
    <select id="getWeightRecordReport" resultType="com.hvisions.purchase.dto.purchase.report.WeightRecordReportDTO">

        SELECT * FROM (
        SELECT
            ddd.id,ddd.license_plate_number,ddd.material_code,ddd.material_name,ddd.estimated_number,ddd.appearance_weight,ddd.gross_weight,ddd.net_weight,ddd.buckle_weight,ddd.admission_time,ddd.appearance_time,
            CASE ddd.state
            WHEN 0 THEN
            "未入场"
            WHEN 1 THEN
            "已入场"
            WHEN 2 THEN
            "可卸货"
            WHEN 3 THEN
            "卸货完成"
            WHEN 4 THEN
            "出门"
            WHEN 5 THEN
            "待处理"
            WHEN 6 THEN
            "拒收"
            WHEN 7 THEN
            "复检"
            WHEN 8 THEN
            "特殊退货"
            WHEN 9 THEN
            "已收货"
            END as state,
            CASE ddd.inspect_state
            WHEN 0 THEN
            "待检验"
            WHEN 1 THEN
            "质检中"
            WHEN 2 THEN
            "合格"
            WHEN 3 THEN
            "不合格"
            END as inspect_state
            ,(ddd.gross_weight - ddd.appearance_weight) actual_weight,IF(ddd.buckle_weight > 0,1,0) is_blckle,
            IF(posting_state =1,ddd.net_weight,0) posting_quantity,
            it.*,dd.plan_number,v.`code` vendor_code,v.`name` vendor_name,
            po.sap_order,po.contract_number,l.`name` location_name,pod.unit_price,pod.unit_price_base
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_purchase_order_detail pod ON pod.id = di.order_detail_id AND pod.deleted = 0 AND pod.is_new = 1
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_procurement_planning pl ON pl.id = po.planning_id AND pl.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN t_mp_inventory_location l ON l.id = pl.location_id AND l.deleted = 0
        LEFT JOIN (
        SELECT it.id inspection_id,it.inspection_order,it.associated_document,it.inspection_result quality_result
        FROM t_qa_inspection_task it
        WHERE it.deleted = 0 AND it.id IN (SELECT MAX(i.id) id FROM t_qa_inspection_task i GROUP BY
        i.associated_document)
        ) it ON it.associated_document = ddd.delivery_number

        WHERE ddd.deleted = 0

        <if test="materialCode != null and materialCode != ''">
            AND ddd.`material_code` LIKE concat('%',#{materialCode},'%')
        </if>

        <if test="materialName != null and materialName != ''">
            AND ddd.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="admissionStartTime != null and admissionEndTime ">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{admissionStartTime}, '%Y-%m-%d') AND
            DATE_FORMAT(#{admissionEndTime}, '%Y-%m-%d')
        </if>
        <if test="planNumber != null and planNumber != ''">
            AND dd.plan_number LIKE concat('%',#{planNumber},'%')
        </if>
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="inspectState != null">
            AND ddd.`inspect_state` = #{inspectState}
        </if>

        <if test="postingState != null">
            AND ddd.`posting_state` = #{postingState}
        </if>
        <if test="inspectionOrder != null and inspectionOrder != ''">
            AND it.inspection_order LIKE concat('%',#{inspectionOrder},'%')
        </if>

        <if test="vendorName != null and vendorName != ''">
            AND v.`name` LIKE concat('%',#{vendorName},'%')
        </if>
        ) a
        WHERE 1 = 1
        <if test="isBuckle != null">
            AND a.`is_blckle` = #{isBuckle}
        </if>
    </select>

    <!--    获取原辅料入库报表-->
    <select id="getInStorageReport" resultType="com.hvisions.purchase.dto.purchase.report.InStorageReportDTO">

        SELECT * FROM (
        SELECT
        po.wheat_certificate_batch,
        ddd.id,
        ddd.delivery_number,
        ddd.license_plate_number,
        dd.plan_number,
        ddd.material_code,
        ddd.material_name,
        ddd.state,
        ddd.admission_time,
        ddd.inspect_state,
        ddd.posting_state,
        ddd.net_weight,
        ddd.batch,
        ddd.warehouse_id,
        rl.`code` warehouse_code,
        deo.demand_date,
        IF(ddd.buckle_weight > 0,1,0) is_blckle
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management rl ON rl.id = ddd.warehouse_id AND rl.deleted = 0

        WHERE ddd.deleted = 0 AND ddd.material_code != "0000"
        and ddd.warehouse_id is not null
        <if test="types != null and types != ''">
            AND FIND_IN_SET(ddd.`type`,#{types})
        </if>
        <if test="materialCodes != null and materialCodes != ''">
            AND FIND_IN_SET(ddd.`material_code`,#{materialCodes})
        </if>
        <if test="vendorCodes != null and vendorCodes != ''">
            AND FIND_IN_SET(v.`code`,#{vendorCodes})
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>
        <if test="startPostDate != null and endPostDate != null">
            AND DATE_FORMAT(ddd.posting_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startPostDate}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endPostDate}, '%Y-%m-%d')
        </if>
        <if test="planNumber != null and planNumber != ''">
            AND dd.plan_number LIKE concat('%',#{planNumber},'%')
        </if>
        <if test="postingState != null">
            AND ddd.`posting_state` = #{postingState}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND rl.`code` LIKE concat('%',#{warehouseCode},'%')
        </if>
        ORDER BY deo.demand_date desc
        ) a
        WHERE 1 = 1
        <if test="isBuckle != null">
            AND a.`is_blckle` = #{isBuckle}
        </if>
    </select>

    <select id="getRedSorghumReceiveReport" resultType="com.hvisions.purchase.dto.purchase.report.RedSorghumReceiveReportDTO">
        SELECT dd.id,dd.plan_number,deo.demand_date,dd.material_name,dd.material_code,dd.deliver_quantity,
            po.*,
            IFNULL(a.arrival_weight,0) arrival_weight,
            IFNULL(a.posting_quantity,0) postingWeight,
            FORMAT((IFNULL(a.posting_quantity,0) / dd.deliver_quantity * 100),2)  completeRate,
        a.total_batch,a.unqualified_batch,a.buckle_batch

        FROM t_mp_daily_delivery dd
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN (
            SELECT po.id order_id, po.order_no,v.`name` vendor_name,v.`code` vendor_code,po.contract_number,
                ANY_VALUE(pd.unit_price) unit_price,ANY_VALUE(pd.unit_price_base) unit_price_base,ANY_VALUE(pd.uom) uom
            FROM  t_mp_purchase_order po
            LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
            LEFT JOIN t_mp_purchase_order_detail pd ON pd.order_id = po.id AND pd.deleted = 0 AND pd.is_new = 1
            GROUP BY po.id
        ) po ON dd.purchase_id = po.order_id
        LEFT JOIN (
            SELECT
            di.delivery_id,
            di.arrival_weight,
            di.posting_quantity,
            COUNT(ddd.id) total_batch,
            SUM(CASE it.inspection_result WHEN "不合格" THEN 1 ELSE 0 END) unqualified_batch,
            SUM(IF(ddd.buckle_weight > 0,1,0)) buckle_batch
            FROM t_mp_delivery_item di
            LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0 AND ddd.material_code != "0000"
            LEFT JOIN (
            SELECT it.id inspection_id,it.inspection_result,it.associated_document
            FROM t_qa_inspection_task it
            WHERE it.id IN (SELECT MAX(i.id) id FROM t_qa_inspection_task i where i.deleted = 0 GROUP BY i.associated_document)
            ) it ON it.associated_document = ddd.delivery_number
            WHERE di.deleted = 0
            GROUP BY di.id
        ) a ON a.delivery_id = dd.id
        WHERE dd.deleted = 0
        <if test="materialCodes != null and materialCodes != ''">
            AND FIND_IN_SET(dd.`material_code`,#{materialCodes})
        </if>
         <if test="vendorCodes != null and vendorCodes != ''">
            AND FIND_IN_SET(po.`vendor_code`,#{vendorCodes})
        </if>
        <if test="planNumber != null and planNumber != ''">
            AND dd.plan_number LIKE concat('%',#{planNumber},'%')
        </if>
        <if test="beginTime != null and endTime != null ">
            AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{beginTime}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>
        order by deo.demand_date desc
    </select>

    <select id="getRedSorghumReceiveSum" resultType="com.hvisions.purchase.dto.purchase.report.RedSorghumReceiveSumDTO">
        select a.material_code,a.material_name, SUM(a.arrival_weight) arrival_weight
        from (
            SELECT dd.id,dd.plan_number,dd.create_time,dd.material_name,dd.material_code,dd.deliver_quantity,
            po.*,
            IFNULL(a.arrival_weight,0) arrival_weight,
            IFNULL(a.posting_quantity,0) postingWeight,
            a.total_batch,a.unqualified_batch,a.buckle_batch
            FROM t_mp_daily_delivery dd
            LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
            LEFT JOIN (
            SELECT po.id order_id, po.order_no,v.`name` vendor_name,v.`code` vendor_code,
            ANY_VALUE(pd.unit_price) unit_price,ANY_VALUE(pd.unit_price_base) unit_price_base,ANY_VALUE(pd.uom) uom
            FROM  t_mp_purchase_order po
            LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
            LEFT JOIN t_mp_purchase_order_detail pd ON pd.order_id = po.id AND pd.deleted = 0 AND pd.is_new = 1
            GROUP BY po.id
            ) po ON dd.purchase_id = po.order_id
            LEFT JOIN (
            SELECT
            di.delivery_id,
            di.arrival_weight,
            di.posting_quantity,
            SUM(CASE it.inspection_result WHEN "合格" THEN 1 ELSE 0 END) total_batch,
            SUM(CASE it.inspection_result WHEN "不合格" THEN 1 ELSE 0 END) unqualified_batch,
            SUM(IF(ddd.buckle_weight > 0,1,0)) buckle_batch
            FROM t_mp_delivery_item di
            LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0 AND ddd.material_code != "0000"
            LEFT JOIN (
            SELECT it.id inspection_id,it.inspection_result,it.associated_document
            FROM t_qa_inspection_task it
            WHERE it.id IN (SELECT MAX(i.id) id FROM t_qa_inspection_task i GROUP BY i.associated_document)
            ) it ON it.associated_document = ddd.delivery_number
            WHERE di.deleted = 0
            GROUP BY di.id
            ) a ON a.delivery_id = dd.id
            WHERE dd.deleted = 0
            <if test="materialCodes != null and materialCodes != ''">
                AND FIND_IN_SET(dd.`material_code`,#{materialCodes})
            </if>
            <if test="vendorCodes != null and vendorCodes != ''">
                AND FIND_IN_SET(po.`vendor_code`,#{vendorCodes})
            </if>
            <if test="planNumber != null and planNumber != ''">
                AND dd.plan_number LIKE concat('%',#{planNumber},'%')
            </if>
            <if test="beginTime != null and endTime != null ">
                AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
                DATE_FORMAT(#{beginTime}, '%Y-%m-%d') AND
                DATE_FORMAT(#{endTime}, '%Y-%m-%d')
            </if>
        ) a
        GROUP BY a.material_code,a.material_name
    </select>

    <!-- 根据物料类型分类，汇总车辆已出门未收数量、已收未过账数量 -->
    <select id="getSummaryQtyByMaterialType"
            resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryQtySummaryDto">
        SELECT a.*,b.notReceiveQty,c.notPostingQty
        FROM (
            SELECT m.material_type
            FROM t_mp_daily_delivery_detail ddd
            LEFT JOIN materials.hv_bm_material m ON ddd.material_code = m.material_code
            WHERE m.material_type IS NOT NULL
            GROUP BY m.material_type
        ) a
        LEFT JOIN (
                SELECT m.material_type,SUM(ddd.leave_net_weight) notReceiveQty
                FROM t_mp_daily_delivery_detail ddd
                LEFT JOIN materials.hv_bm_material m ON ddd.material_code = m.material_code
                WHERE ddd.state = 4
                GROUP BY m.material_type
        ) b ON a.material_type = b.material_type
        LEFT JOIN (
            SELECT m.material_type,SUM(ddd.leave_net_weight) notPostingQty
            FROM t_mp_daily_delivery_detail ddd
            LEFT JOIN materials.hv_bm_material m ON ddd.material_code = m.material_code
            WHERE ddd.state = 9 AND ddd.posting_state  = 0
            GROUP BY m.material_type

        ) c ON c.material_type = a.material_type



    </select>

    <select id="selectTodayScheduleList" resultType="com.hvisions.purchase.purchase.entity.TMpDailyDeliveryDetail">
        SELECT *
        FROM t_mp_daily_delivery_detail
        WHERE DATE(create_time) = CURDATE() and sync_state = "0" and deleted = "0"
    </select>

    <select id="getFirstInTimeByDDId" resultType="java.util.Date">
        SELECT ddd.admission_time
        FROM t_mp_daily_delivery_detail ddd
                LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.material_code != "0000" AND di.delivery_id = #{id}
        ORDER BY ddd.admission_time ASC
        LIMIT 1
    </select>

    <select id="getLastInTimeByDDId" resultType="java.util.Date">
        SELECT ddd.admission_time
        FROM t_mp_daily_delivery_detail ddd
                LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.material_code != "0000" AND di.delivery_id = #{id}
        ORDER BY ddd.admission_time DESC
        LIMIT 1
    </select>

    <!-- 根据关联单号获取车牌号信息 -->
        <select id="getDeliveryCarInfo" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryDetailInfoDTO">
            SELECT ddd.license_plate_number,il.`name` location_name,deo.demand_date
            FROM t_mp_daily_delivery_detail ddd
            LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
            LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
            LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
            LEFT JOIN t_mp_inventory_location il ON il.id = deo.location_id AND il.deleted = 0

            WHERE delivery_number = #{deliveryNumber}
        </select>

    <select id="getVehicleByBatch" resultType="com.hvisions.purchase.dto.purchase.receiving.VehicleBatchDTO">
        SELECT ddd.* ,GROUP_CONCAT(it.id) inspectionIds,GROUP_CONCAT(it.inspection_order) inspectionOrders,GROUP_CONCAT(it.sampling_code) samplingCodes,
            v.`name` vendor_name,v.`code` vendor_code
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0

        LEFT JOIN t_qa_inspection_task it ON it.associated_document = ddd.delivery_number AND it.deleted = 0
        WHERE ddd.deleted = 0
            AND FIND_IN_SET(ddd.batch,#{batch})
        GROUP BY ddd.id
    </select>

    <select id="getDzVehicleReport" resultType="com.hvisions.purchase.dto.purchase.receiving.DzVehicleReportDTO">
        select a.* from (
            SELECT ddd.license_plate_number,ddd.receipt_place,SUM(ddd.net_weight) `sum`,COUNT(id) `count`
            FROM `t_mp_daily_delivery_detail` ddd
            WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟"
            <if test="state != null">
                AND ddd.`state` = #{state}
            </if>
            <if test="licensePlateNumber != null and licensePlateNumber != ''">
                AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
            </if>
            <if test="receiptPlace != null and receiptPlace != ''">
                AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
            </if>
            <if test="startDate != null and endDate != null">
                AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
                DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
            </if>
            GROUP BY ddd.license_plate_number,ddd.receipt_place
        )a
    </select>

    <select id="getWheatWeighDetail" resultType="com.hvisions.purchase.dto.purchase.wheat.vehicle.WheatWeighDetailDTO">
        SELECT SUM(ddd.net_weight) overweightWeight,COUNT(ddd.id) frequency,any_value( v.`name`) vendor_name
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        WHERE ddd.deleted = 0
        <if test="materialCode != null and materialCode != ''">
            AND ddd.`material_code` = #{materialCode}
        </if>
        <if test="warehouseTaskId != null and warehouseTaskId != ''">
            AND ddd.`warehouse_task_id` = #{warehouseTaskId}
        </if>
        <if test="startTime != null and endTime != null ">
            AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND
            DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        </if>
    </select>

    <select id="getDzWeeklyReport" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DzWeeklyReportDetailDTO">
        SELECT
        SUM( net_weight ) AS netWeight,
        receipt_place AS receiptPlace,
        DATE_FORMAT(#{endDate}, '%Y-%m-%d') as createTime
        FROM `t_mp_daily_delivery_detail` ddd
        WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟"
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="receiptPlace != null and receiptPlace != ''">
            AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="appearanceStartTime != null and appearanceEndTime != null">
            AND DATE_FORMAT(ddd.appearance_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{appearanceStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{appearanceEndTime}, '%Y-%m-%d')
        </if>
        GROUP BY receipt_place
    </select>

    <select id="getDzQuarterReport" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DzMonthlyLedgerDTO">
        SELECT
        SUM( IFNULL(net_weight,0) ) AS netWeight,
        DATE_FORMAT(ddd.admission_time, '%Y-%m') as month
        FROM `t_mp_daily_delivery_detail` ddd
        WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟"
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="receiptPlace != null and receiptPlace != ''">
            AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="appearanceStartTime != null and appearanceEndTime != null">
            AND DATE_FORMAT(ddd.appearance_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{appearanceStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{appearanceEndTime}, '%Y-%m-%d')
        </if>
        GROUP BY DATE_FORMAT(ddd.admission_time, '%Y-%m')
    </select>

    <select id="getDzQuarterUnitLedgerReport" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DzUnitLedgerDTO">
        SELECT
        SUM( IFNULL(net_weight,0) ) AS netWeight,
        receipt_place as receiptPlace
        FROM `t_mp_daily_delivery_detail` ddd
        WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟"
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="receiptPlace != null and receiptPlace != ''">
            AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="appearanceStartTime != null and appearanceEndTime != null">
            AND DATE_FORMAT(ddd.appearance_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{appearanceStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{appearanceEndTime}, '%Y-%m-%d')
        </if>
        GROUP BY receipt_place
    </select>

    <select id="getDzSunReport" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DzSunReportDetailDTO">
        SELECT
            IFNULL(net_weight,0) AS netWeight,
        receipt_place as receiptPlace,
        CASE
        WHEN receipt_place LIKE '四川点击%' THEN 'SW13'
        WHEN receipt_place LIKE '泸州汉基%' THEN 'SW13'
        WHEN receipt_place LIKE '四川正羽%' THEN 'SW59'
        ELSE receipt_place END  as receiptPlaceCode,
            appearance_time,
            license_plate_number
        FROM `t_mp_daily_delivery_detail` ddd
        WHERE ddd.deleted = 0 and ddd.`material_name` = "丢糟" and ddd.receipt_place is not null
        <if test="state != null">
            AND ddd.`state` = #{state}
        </if>
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND ddd.`license_plate_number` LIKE concat('%',#{licensePlateNumber},'%')
        </if>
        <if test="receiptPlace != null and receiptPlace != ''">
            AND ddd.`receipt_place` LIKE concat('%',#{receiptPlace},'%')
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="appearanceStartTime != null and appearanceEndTime != null">
            AND DATE_FORMAT(ddd.appearance_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{appearanceStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{appearanceEndTime}, '%Y-%m-%d')
        </if>
        ORDER BY ddd.id DESC
    </select>
</mapper>

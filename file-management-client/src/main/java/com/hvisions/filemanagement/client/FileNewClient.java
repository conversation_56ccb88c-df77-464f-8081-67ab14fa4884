package com.hvisions.filemanagement.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.filemanagement.dto.FileDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title:FileNewClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/8</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "file-management", path = "/file", fallbackFactory = FileNewClientFallBack.class)
public interface FileNewClient {


    /**
     * 根据文件id获取文件详情
     *
     * @param fileId 文件id
     * @return 文件信息
     */
    @GetMapping("/getFileDetail/{fileId}")
    @ApiOperation(value = "根据文件id获取文件详情")
    ResultVO<FileDTO> getFileDetail(@PathVariable int fileId);

    /**
     * 批量获取文件详情
     *
     * @param fileIds 文件id列表
     * @return 文件详情信息
     */
    @PostMapping("/getFileDetailBatch")
    @ApiOperation(value = "批量获取文件详情")
    ResultVO<List<FileDTO>> getFileDetailBatch(@RequestBody List<Integer> fileIds);


}

package com.hvisions.filemanagement.client;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.filemanagement.dto.FileDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:FileNewClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/8</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class FileNewClientFallBack extends BaseFallbackFactory<FileNewClient> {
    @Override
    public FileNewClient getFallBack(ResultVO vo) {
        return new FileNewClient() {
            @Override
            public ResultVO<FileDTO> getFileDetail(int fileId) {
                return vo;
            }

            @Override
            public ResultVO<List<FileDTO>> getFileDetailBatch(List<Integer> fileIds) {
                return vo;
            }
        };
    }
}

package com.hvisions.filemanagement.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.filemanagement.dto.FileDTO;
import com.hvisions.filemanagement.dto.FileExcelExportDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title: demoClient</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "file-management",fallbackFactory = FileFallBackFactory.class)
public interface FileClient {


    /**
     * 批量获取文件详情
     *
     * @param fileIds 文件id列表
     * @return 文件详情信息
     */
    @PostMapping("/file/getFileDetailBatch")
    @ApiOperation(value = "批量获取文件详情")
    ResultVO<List<FileDTO>> getFileDetailBatch(@RequestBody List<Integer> fileIds);

    /**
     * 根据文件id下载文件
     *
     * @param fileId 文件id
     * @return vo
     */
    @GetMapping("/file/downloadFileResult/{fileId}")
    @ApiOperation(value = "根据文件id下载文件,有文件名")
    ResultVO<FileExcelExportDto> downloadFileWithResult(@PathVariable int fileId);
}

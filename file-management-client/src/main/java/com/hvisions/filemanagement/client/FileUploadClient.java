package com.hvisions.filemanagement.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.filemanagement.dto.FileDTO;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@FeignClient(name = "file-management", configuration = FileUploadClient.MultipartSupportConfig.class)
public interface FileUploadClient {
    /**
     * 上传文件
     *
     * @param file         文件信息
     * @param name         文件名称
     * @param fileTypeName 文件类型名称
     * @return 文件详情
     */
    @PostMapping(value = "/file/uploadFileByTypeNameAndName/{name}/{fileTypeName}",
            produces = {MediaType.APPLICATION_JSON_UTF8_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "上传文件 文件类型和上传用户名称 3.0")
    ResultVO<FileDTO> uploadFileByTypeNameAndName(@RequestPart(value = "file") MultipartFile file,
                                                  @PathVariable(value = "name") String name,
                                                  @PathVariable(value = "fileTypeName") String fileTypeName);

    class MultipartSupportConfig {
        @Bean
        public Encoder feignFormEncoder() {
            return new SpringFormEncoder();
        }
    }

}

package com.hvisions.filemanagement.client;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.filemanagement.dto.FileDTO;
import com.hvisions.filemanagement.dto.FileExcelExportDto;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: FileFallBackFactory</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/11</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class FileFallBackFactory extends BaseFallbackFactory<FileClient> {
    @Override
    public FileClient getFallBack(ResultVO vo) {
        return new FileClient() {
            @Override
            public ResultVO<List<FileDTO>> getFileDetailBatch(List<Integer> fileIds) {
                return vo;
            }

            @Override
            public ResultVO<FileExcelExportDto> downloadFileWithResult(int fileId) {
                return vo;
            }
        };
    }
}










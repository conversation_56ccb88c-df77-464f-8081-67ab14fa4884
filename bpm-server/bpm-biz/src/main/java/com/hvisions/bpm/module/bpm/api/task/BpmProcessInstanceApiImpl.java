package com.hvisions.bpm.module.bpm.api.task;

import com.hvisions.bpm.framework.common.pojo.CommonResult;
import com.hvisions.bpm.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.hvisions.bpm.module.bpm.service.task.BpmProcessInstanceService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.hvisions.bpm.framework.common.pojo.CommonResult.success;

/**
 * Flowable 流程实例 Api 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@RestController
@Validated
public class BpmProcessInstanceApiImpl implements BpmProcessInstanceApi {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Override
    public CommonResult<String> createProcessInstance(Long userId, BpmProcessInstanceCreateReqDTO reqDTO) {
        return success(processInstanceService.createProcessInstance(userId, reqDTO));
    }

}

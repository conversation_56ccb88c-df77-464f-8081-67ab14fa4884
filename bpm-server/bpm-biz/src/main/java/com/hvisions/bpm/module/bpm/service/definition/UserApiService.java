package com.hvisions.bpm.module.bpm.service.definition;

import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.AdminUserRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: UserApiService
 * @description:
 * @date 2025/2/13 10:04
 */
public interface UserApiService {

    AdminUserRespDTO getUser(Long userId);

    List<AdminUserRespDTO> getUserList(List<Long> userIdList);

    List<AdminUserRespDTO> getUserList(Set<Long> userIdList);

    List<AdminUserRespDTO> getUserListByDeptIds(Set<Long> deptIdList);

    List<AdminUserRespDTO> getUserListByPostIds(Set<Long> postIds);

    Map<Long, AdminUserRespDTO> getUserMap(Set<Long> ids);

    Map<Long, AdminUserRespDTO> getUserMap(List<Long> ids);

    List<Integer> getUserRole(Integer userId);

    List<AdminUserRespDTO> getRoleUser(Integer roleId);

    List<AdminUserRespDTO> getRoleUserList(List<Integer> roleIdList);

    AdminUserRespDTO getTokenUser();

}

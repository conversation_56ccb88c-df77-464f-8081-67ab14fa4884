package com.hvisions.bpm.module.bpm.service.definition;

import cn.hutool.core.collection.CollUtil;
import com.hvisions.bpm.framework.common.util.collection.CollectionUtils;
import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.DeptRespDTO;
import com.hvisions.bpm.module.bpm.dal.mysql.definition.DeptApiMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.hvisions.bpm.module.bpm.util.ListUtil.listToList;
import static com.hvisions.bpm.module.bpm.util.ListUtil.setToList;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: DeptApiServiceImpl
 * @description:
 * @date 2025/2/13 14:53
 */

@Service
@Validated
public class DeptApiServiceImpl implements DeptApiService{

    @Resource
    private DeptApiMapper deptApiMapper;

    /**
     * 获得部门信息
     * @param deptId
     * @return
     */
    @Override
    public DeptRespDTO getDept(Long deptId) {
        return deptApiMapper.selectDept(deptId.intValue());
    }

    /**
     * 获得部门信息数组
     * @param deptIdList
     * @return
     */
    @Override
    public List<DeptRespDTO> getDeptList(List<Long> deptIdList) {
        if (CollUtil.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }
        return deptApiMapper.selectDeptList(listToList(deptIdList));
    }

    /**
     * 获得部门信息数组
     * @param deptIdList
     * @return
     */
    @Override
    public List<DeptRespDTO> getDeptList(Set<Long> deptIdList) {
        if (CollUtil.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }
        return deptApiMapper.selectDeptList(setToList(deptIdList));
    }

    @Override
    public Map<Long, DeptRespDTO> getDeptMap(List<Long> deptIdList) {
        List<DeptRespDTO> list = getDeptList(deptIdList);
        return CollectionUtils.convertMap(list, DeptRespDTO::getId);
    }

    @Override
    public Map<Long, DeptRespDTO> getDeptMap(Set<Long> deptIdList) {
        if (CollUtil.isEmpty(deptIdList)) {
            return null;
        }
        List<DeptRespDTO> list = deptApiMapper.selectDeptList(setToList(deptIdList));
        return CollectionUtils.convertMap(list, DeptRespDTO::getId);
    }
}

package com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.strategy.user;

import com.hvisions.bpm.framework.common.util.string.StrUtils;
import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.AdminUserRespDTO;
import com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.BpmTaskCandidateStrategy;
import com.hvisions.bpm.module.bpm.framework.flowable.core.enums.BpmTaskCandidateStrategyEnum;
import com.hvisions.bpm.module.bpm.service.definition.UserApiService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static com.hvisions.bpm.module.bpm.enums.ErrorCodeConstants.POST_NOT_FOUND;
import static com.hvisions.bpm.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hvisions.bpm.framework.common.util.collection.CollectionUtils.convertSet;

/**
 * 岗位 {@link BpmTaskCandidateStrategy} 实现类
 *
 * <AUTHOR>
 */
@Component
public class BpmTaskCandidatePostStrategy implements BpmTaskCandidateStrategy {

    @Resource
    private UserApiService userApiService;

    @Override
    public BpmTaskCandidateStrategyEnum getStrategy() {
        return BpmTaskCandidateStrategyEnum.POST;
    }

    @Override
    public void validateParam(String param) {
        Set<Long> postIds = StrUtils.splitToLongSet(param);
        if(org.springframework.util.CollectionUtils.isEmpty(postIds)){
            throw exception(POST_NOT_FOUND);
        }
    }

    @Override
    public Set<Long> calculateUsers(String param) {
        Set<Long> postIds = StrUtils.splitToLongSet(param);
        List<AdminUserRespDTO> users = userApiService.getUserListByPostIds(postIds);
        return convertSet(users, AdminUserRespDTO::getId);
    }

}
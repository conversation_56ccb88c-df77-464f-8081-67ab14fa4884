package com.hvisions.bpm.module.bpm.dal.mysql.definition;

import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.AdminUserRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: UserApiMapper
 * @description:
 * @date 2025/2/13 11:11
 */

@Mapper
public interface UserApiMapper {

    /**
     * 根据用户获取用户列表
     * @param userIdList
     * @return
     */
    List<AdminUserRespDTO> selectUserList(@Param("userIdList") List<Integer> userIdList);

    /**
     * 根据用户获取用户
     * @param userId
     * @return
     */
    AdminUserRespDTO selectUser(@Param("userId") Integer userId);

    /**
     * 获得指定部门的用户数组
     * @param deptIdList
     * @return
     */
    List<AdminUserRespDTO> selectUserListByDeptIds(@Param("deptIdList") List<Integer> deptIdList);

    /**
     * 获取用户的角色
     * @param userId
     * @return
     */
    List<Integer> selectUserRole(@Param("userId") Integer userId);

    /**
     * 根据角色获取用户
     * @param roleId
     * @return
     */
    List<AdminUserRespDTO> getRoleUser(@Param("roleId") Integer roleId);


    List<AdminUserRespDTO> getRoleUserList(@Param("roleIdList") List<Integer> roleIdList);
}

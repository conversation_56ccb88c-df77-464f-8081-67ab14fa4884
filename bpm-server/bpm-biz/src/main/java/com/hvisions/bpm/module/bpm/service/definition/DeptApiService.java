package com.hvisions.bpm.module.bpm.service.definition;


import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.DeptRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: DeptApiService
 * @description:
 * @date 2025/2/13 14:52
 */
public interface DeptApiService {

    DeptRespDTO getDept(Long deptId);

    List<DeptRespDTO> getDeptList(List<Long> deptIdList);

    List<DeptRespDTO> getDeptList(Set<Long> deptIdList);

    Map<Long, DeptRespDTO> getDeptMap(List<Long> deptIdList);

    Map<Long, DeptRespDTO> getDeptMap(Set<Long> deptIdList);
}

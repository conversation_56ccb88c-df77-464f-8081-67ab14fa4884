package com.hvisions.bpm.module.bpm.util;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ListUtil
 * @description:
 * @date 2025/2/13 14:59
 */
public class ListUtil {

    /**
     * List<Integer> 转 Set<Long>
     * @param list
     * @return
     */
    public static Set<Long> listToSet(List<Integer> list){
        // 将List<Integer>转换为Set<Long>
        return list.stream().map(Integer::longValue).collect(Collectors.toSet());
    }

    /**
     * Set<Long> 转 List<Integer>
     * @param list
     * @return
     */
    public static List<Integer> setToList(Set<Long> list){
        // 将List<Integer>转换为Set<Long>
        return list.stream().map(Long::intValue).collect(Collectors.toList());
    }


    /**
     * List<Long> 转 List<Integer>
     * @param list
     * @return
     */
    public static List<Integer> listToList(List<Long> list){
        // 将List<Long>List<Integer>
        return list.stream().map(Long::intValue).collect(Collectors.toList());
    }
}

package com.hvisions.bpm.module.bpm.service.definition.dto;

import com.hvisions.bpm.module.bpm.enums.definition.BpmModelFormTypeEnum;
import com.hvisions.bpm.module.bpm.dal.dataobject.definition.BpmProcessDefinitionInfoDO;
import lombok.Data;

/**
 * BPM 流程 MetaInfo Response DTO
 * 主要用于 { Model#setMetaInfo(String)} 的存储
 *
 * 最终，它的字段和 {@link BpmProcessDefinitionInfoDO} 是一致的
 *
 * <AUTHOR>
 */
@Data
public class BpmModelMetaInfoRespDTO {

    /**
     * 流程图标
     */
    private String icon;
    /**
     * 流程描述
     */
    private String description;

    /**
     * 表单类型
     */
    private Integer formType;
    /**
     * 表单编号
     * 在表单类型为 {@link BpmModelFormTypeEnum#NORMAL} 时
     */
    private Long formId;
    /**
     * 自定义表单的提交路径，使用 Vue 的路由地址
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时
     */
    private String formCustomCreatePath;
    /**
     * 自定义表单的查看路径，使用 Vue 的路由地址
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时
     */
    private String formCustomViewPath;

}

package com.hvisions.bpm.module.bpm.service.definition;

import cn.hutool.core.collection.CollUtil;
import com.hvisions.bpm.framework.common.util.collection.CollectionUtils;
import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.AdminUserRespDTO;
import com.hvisions.bpm.module.bpm.dal.mysql.definition.UserApiMapper;
import com.hvisions.bpm.module.bpm.util.ListUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.hvisions.bpm.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: UserApiServiceImpl
 * @description:
 * @date 2025/2/13 10:05
 */

@Service
@Validated
public class UserApiServiceImpl implements UserApiService{

    @Resource
    private UserApiMapper userApiMapper;


    /**
     * 通过用户 ID 查询用户
     * @param userId
     * @return
     */
    @Override
    public AdminUserRespDTO getUser(Long userId) {
        AdminUserRespDTO respDTO = userApiMapper.selectUser(userId.intValue());
        //给用户设置角色
        setUserRole(respDTO);

        return respDTO;
    }

    /**
     * 通过用户 ID 查询用户们
     * @param userIdList
     * @return
     */
    @Override
    public List<AdminUserRespDTO> getUserList(List<Long> userIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        List<AdminUserRespDTO> userRespDTOList = userApiMapper.selectUserList(ListUtil.listToList(userIdList));
        //给用户设置角色
        setUserRole(userRespDTOList);
        return userRespDTOList;
    }

    /**
     * 通过用户 ID 查询用户们
     * @param userIdList
     * @return
     */
    @Override
    public List<AdminUserRespDTO> getUserList(Set<Long> userIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        List<AdminUserRespDTO> userRespDTOList = userApiMapper.selectUserList(ListUtil.setToList(userIdList));

        //给用户设置角色
        setUserRole(userRespDTOList);

        return userRespDTOList;
    }

    /**
     * 获得指定部门的用户数组
     * @param deptIdList
     * @return
     */
    @Override
    public List<AdminUserRespDTO> getUserListByDeptIds(Set<Long> deptIdList) {
        if (CollUtil.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }
        List<AdminUserRespDTO> userRespDTOList = userApiMapper.selectUserListByDeptIds(ListUtil.setToList(deptIdList));
        //给用户设置角色
        setUserRole(userRespDTOList);
        return userRespDTOList;
    }


    @Override
    public Map<Long, AdminUserRespDTO> getUserMap(Set<Long> ids) {
        List<AdminUserRespDTO> users = getUserList(ids);
        return CollectionUtils.convertMap(users, AdminUserRespDTO::getId);
    }

    @Override
    public Map<Long, AdminUserRespDTO> getUserMap(List<Long> ids) {
        List<AdminUserRespDTO> users = getUserList(ids);
        return CollectionUtils.convertMap(users, AdminUserRespDTO::getId);
    }

    /**
     * 获取用户的角色
     * @param userId
     * @return
     */
    @Override
    public List<Integer> getUserRole(Integer userId) {
        if (null==userId) {
            return Collections.emptyList();
        }
        return userApiMapper.selectUserRole(userId);
    }

    /**
     * 根据角色查询用户
     * @param roleId
     * @return
     */
    @Override
    public List<AdminUserRespDTO> getRoleUser(Integer roleId) {
        if (null==roleId) {
            return Collections.emptyList();
        }
        List<AdminUserRespDTO> roleUser = userApiMapper.getRoleUser(roleId);
        //给用户设置角色
        setUserRole(roleUser);
        return roleUser;
    }


    /**
     * 根据角色查询用户
     * @param roleIdList
     * @return
     */
    @Override
    public List<AdminUserRespDTO> getRoleUserList(List<Integer> roleIdList) {
        if (CollUtil.isEmpty(roleIdList)) {
            return Collections.emptyList();
        }
        List<AdminUserRespDTO> roleUserList = userApiMapper.getRoleUserList(roleIdList);
        //给用户设置角色
        setUserRole(roleUserList);

        return roleUserList;
    }

    /**
     * 通过登录人查询用户
     * @return
     */
    @Override
    public AdminUserRespDTO getTokenUser() {
        Long loginUserId = getLoginUserId();
        return getUser(loginUserId);
    }


    /**
     * 根据角色查询用户
     * @param postIds
     * @return
     */
    @Override
    public List<AdminUserRespDTO> getUserListByPostIds(Set<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        List<AdminUserRespDTO> roleUserList = getRoleUserList(ListUtil.setToList(postIds));
        if(!org.springframework.util.CollectionUtils.isEmpty(roleUserList)){
            //给用户设置角色
            setUserRole(roleUserList);
        }
        return roleUserList;
    }


    /**
     * 给用户设置角色
     * @param userRespDTOList
     */
    private void setUserRole(List<AdminUserRespDTO> userRespDTOList){
        userRespDTOList.forEach(v->{
            List<Integer> userRole = getUserRole(v.getId().intValue());
            v.setPostIds(ListUtil.listToSet(userRole));
        });
    }

    /**
     * 给用户设置角色
     * @param userRespDTO
     */
    private void setUserRole(AdminUserRespDTO userRespDTO){
        List<Integer> userRole = getUserRole(userRespDTO.getId().intValue());
        userRespDTO.setPostIds(ListUtil.listToSet(userRole));
    }
}

package com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.strategy.user;

import cn.hutool.core.text.StrPool;
import com.hvisions.bpm.framework.common.exception.ServiceException;
import com.hvisions.bpm.framework.common.util.string.StrUtils;
import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.AdminUserRespDTO;
import com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.BpmTaskCandidateStrategy;
import com.hvisions.bpm.module.bpm.framework.flowable.core.enums.BpmTaskCandidateStrategyEnum;
import com.hvisions.bpm.module.bpm.service.definition.UserApiService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 用户 {@link BpmTaskCandidateStrategy} 实现类
 *
 * <AUTHOR>
 */
@Component
public class BpmTaskCandidateUserStrategy implements BpmTaskCandidateStrategy {

    @Resource
    private UserApiService userApiService;

    @Override
    public BpmTaskCandidateStrategyEnum getStrategy() {
        return BpmTaskCandidateStrategyEnum.USER;
    }

    @Override
    public void validateParam(String param) {
        Set<Long> userIdList = StrUtils.splitToLongSet(param);
        userIdList.forEach(v->{
            AdminUserRespDTO userRespDTO = userApiService.getUser(v);
            if(null==userRespDTO){
                // 业务异常
                throw new ServiceException(500, "未查询到账号,账号ID:"+v);
            }
        });

    }

    @Override
    public LinkedHashSet<Long> calculateUsers(String param) {
        return new LinkedHashSet<>(StrUtils.splitToLong(param, StrPool.COMMA));
    }

}
package com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.strategy.dept;

import com.hvisions.bpm.framework.common.exception.ServiceException;
import com.hvisions.bpm.framework.common.util.string.StrUtils;
import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.DeptRespDTO;
import com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.BpmTaskCandidateStrategy;
import com.hvisions.bpm.module.bpm.framework.flowable.core.enums.BpmTaskCandidateStrategyEnum;
import com.hvisions.bpm.module.bpm.service.definition.DeptApiService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static com.hvisions.bpm.framework.common.util.collection.CollectionUtils.convertSet;

/**
 * 部门的负责人 {@link BpmTaskCandidateStrategy} 实现类
 *
 * <AUTHOR>
 */
@Component
public class BpmTaskCandidateDeptLeaderStrategy implements BpmTaskCandidateStrategy {

    @Resource
    private DeptApiService deptApiService;

    @Override
    public BpmTaskCandidateStrategyEnum getStrategy() {
        return BpmTaskCandidateStrategyEnum.DEPT_LEADER;
    }

    @Override
    public void validateParam(String param) {
        Set<Long> deptIds = StrUtils.splitToLongSet(param);
        deptIds.forEach(v->{
            DeptRespDTO dept = deptApiService.getDept(v);
            if(null==dept){
                // 业务异常
                throw new ServiceException(500, "未查询到部门，部门id:"+v);
            }
        });
    }

    @Override
    public Set<Long> calculateUsers(String param) {
        Set<Long> deptIds = StrUtils.splitToLongSet(param);
        List<DeptRespDTO> depts = deptApiService.getDeptList(deptIds);
        return convertSet(depts, DeptRespDTO::getLeaderUserId);
    }

}
package com.hvisions.bpm.module.bpm.dal.mysql.definition;

import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.DeptRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: DeptApiMapper
 * @description:
 * @date 2025/2/13 14:56
 */

@Mapper
public interface DeptApiMapper {

    /**
     * 获得部门信息
     * @param deptId
     * @return
     */
    DeptRespDTO selectDept(@Param("deptId") Integer deptId);


    /**
     * 获得部门信息数组
     * @param deptIdList
     * @return
     */
    List<DeptRespDTO> selectDeptList(@Param("deptIdList") List<Integer> deptIdList);

}

package com.hvisions.bpm.module.bpm.dal.mysql.definition;


import com.hvisions.bpm.framework.common.pojo.PageResult;
import com.hvisions.bpm.framework.mybatis.core.mapper.BaseMapperX;
import com.hvisions.bpm.framework.mybatis.core.query.QueryWrapperX;
import com.hvisions.bpm.module.bpm.controller.admin.definition.vo.form.BpmFormPageReqVO;
import com.hvisions.bpm.module.bpm.dal.dataobject.definition.BpmFormDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 动态表单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmFormMapper extends BaseMapperX<BpmFormDO> {

    default PageResult<BpmFormDO> selectPage(BpmFormPageReqVO reqVO) {
        return selectPage(reqVO, new QueryWrapperX<BpmFormDO>()
                .likeIfPresent("name", reqVO.getName())
                .orderByDesc("id"));
    }

}

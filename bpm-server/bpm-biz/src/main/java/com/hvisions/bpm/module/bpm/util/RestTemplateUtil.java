package com.hvisions.bpm.module.bpm.util;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.consts.CookieConst;
import com.hvisions.common.vo.ResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2021/11/11 15:23
 */

@Component
public class RestTemplateUtil {
    private static final Logger logger = LoggerFactory.getLogger(RestTemplateUtil.class);

    @Autowired
    private RestTemplate restTemplate;
    @Resource
    private HttpServletRequest request;
    /**
     * 发送Post请求
     *
     * @param url       发送地址
     * @param paramList 参数
     * @return
     */
    public <T> ResultVO savePostTemplate(String url, List<T> paramList) {
        logger.info("开始发起请求-发送Post请求,传入参数---------->路径:{},参数:{}",url,paramList);

        String param = JSONObject.toJSONString(paramList);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set("token",request.getHeader(CookieConst.AUTH_TOKEN));
        HttpEntity<String> request = new HttpEntity<>(param, headers);

        ResponseEntity<ResultVO> responseEntity = restTemplate.postForEntity(url, request, ResultVO.class);

        logger.info("结束发起请求-发送Post请求,传入参数---------->路径:{},参数:{}",url,paramList);
        return responseEntity.getBody();
    }


    /**
     * 发送Post请求
     *
     * @param url       发送地址
     * @param paramList 参数
     * @return
     */
    public <T> ResultVO savePostTemplate(String url, T paramList) {
        logger.info("开始发起请求-发送Post请求,传入参数---------->路径:{},参数:{}",url,paramList);

        String param = JSONObject.toJSONString(paramList);
        HttpHeaders headers = new HttpHeaders();
        headers.set("token",request.getHeader(CookieConst.AUTH_TOKEN));
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> request = new HttpEntity<>(param, headers);
        ResponseEntity<ResultVO> responseEntity = restTemplate.postForEntity(url, request, ResultVO.class);

        logger.info("结束发起请求-发送Post请求,传入参数---------->路径:{},参数:{}",url,paramList);
        return responseEntity.getBody();
    }

    /**
     * 发送GET请求
     *
     * @param url       发送地址
     * @param jsonParam 参数
     * @return
     */
    public <T> ResultVO saveGetTemplate(String url, T jsonParam) {
        logger.info("开始发起请求-发送GET请求,传入参数---------->路径:{},参数:{}",url,jsonParam);

        url = url.concat("?"+jsonParam);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token",request.getHeader(CookieConst.AUTH_TOKEN));
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity request = new HttpEntity<>(headers);

        ResponseEntity<ResultVO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, ResultVO.class);

        logger.info("结束发起请求-发送GET请求,传入参数---------->路径:{},参数:{}",url,jsonParam);
        return responseEntity.getBody();
    }

    /**
     * 发送GET请求
     *
     * @param url       发送地址
     * @param jsonParam 参数
     * @return
     */
    public <T> ResultVO saveGet(String url, T jsonParam) {
        logger.info("开始发起请求-发送GET请求,传入参数---------->路径:{},参数:{}",url,jsonParam);

        url = url.concat("/"+jsonParam);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token",request.getHeader(CookieConst.AUTH_TOKEN));
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity request = new HttpEntity<>(headers);

        ResponseEntity<ResultVO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, ResultVO.class);

        logger.info("结束发起请求-发送GET请求,传入参数---------->路径:{},参数:{}",url,jsonParam);
        return responseEntity.getBody();
    }

    /**
     * 发送GET请求
     *
     * @param url       发送地址
     * @return
     */
    public <T> ResultVO saveGetTemplate(String url) {
        logger.info("开始发起请求-发送GET请求,传入参数---------->路径:{}",url);

        HttpHeaders headers = new HttpHeaders();
        headers.set("token",request.getHeader(CookieConst.AUTH_TOKEN));
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity request = new HttpEntity<>(headers);

        ResponseEntity<ResultVO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, ResultVO.class);

        logger.info("结束发起请求-发送GET请求,传入参数---------->路径:{}",url);
        return responseEntity.getBody();
    }
}

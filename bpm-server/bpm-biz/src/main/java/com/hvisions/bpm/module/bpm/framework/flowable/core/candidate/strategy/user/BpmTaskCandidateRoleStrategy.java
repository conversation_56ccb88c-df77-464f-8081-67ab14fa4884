package com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.strategy.user;

import com.hvisions.bpm.framework.common.util.string.StrUtils;
import com.hvisions.bpm.module.bpm.controller.admin.definition.dto.AdminUserRespDTO;
import com.hvisions.bpm.module.bpm.framework.flowable.core.candidate.BpmTaskCandidateStrategy;
import com.hvisions.bpm.module.bpm.framework.flowable.core.enums.BpmTaskCandidateStrategyEnum;
import com.hvisions.bpm.module.bpm.service.definition.UserApiService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.hvisions.bpm.module.bpm.enums.ErrorCodeConstants.ROLE_NOT_EXISTS;
import static com.hvisions.bpm.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hvisions.bpm.module.bpm.util.ListUtil.setToList;

/**
 * 角色 {@link BpmTaskCandidateStrategy} 实现类
 *
 * <AUTHOR>
 */
@Component
public class BpmTaskCandidateRoleStrategy implements BpmTaskCandidateStrategy {

//    @Resource
//    private RoleApi roleApi;
//    @Resource
//    private PermissionApi permissionApi;

    @Resource
    private UserApiService userApiService;

    @Override
    public BpmTaskCandidateStrategyEnum getStrategy() {
        return BpmTaskCandidateStrategyEnum.ROLE;
    }

    @Override
    public void validateParam(String param) {
        Set<Long> roleIds = StrUtils.splitToLongSet(param);
        if(CollectionUtils.isEmpty(roleIds)){
            throw exception(ROLE_NOT_EXISTS);
        }
    }

    @Override
    public Set<Long> calculateUsers(String param) {
        Set<Long> roleIds = StrUtils.splitToLongSet(param);
        List<AdminUserRespDTO> roleUserList = userApiService.getRoleUserList(setToList(roleIds));
        if(!CollectionUtils.isEmpty(roleUserList)){
            return roleUserList.stream().map(AdminUserRespDTO::getId).collect(Collectors.toSet());
        }
        return null;
    }

}